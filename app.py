from flask import Flask, render_template, request, redirect, url_for, flash, jsonify
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager, login_required, current_user
from flask_bcrypt import Bcrypt
from datetime import datetime, date, timedelta
import os

# Import de la configuration
from config import Config

# Import de l'instance db
from database import db

# Import des blueprints
from auth import auth_bp

# Import des modèles
from models.user import User, UserRole

# Import des décorateurs
from decorators import login_required_with_role, admin_required, staff_required

def create_app():
    app = Flask(__name__)
    app.config.from_object(Config)

    # Initialisation de la base de données
    db.init_app(app)

    # Initialisation de Flask-Login
    login_manager = LoginManager()
    login_manager.init_app(app)
    login_manager.login_view = 'auth.login'
    login_manager.login_message = 'Vous devez être connecté pour accéder à cette page.'
    login_manager.login_message_category = 'warning'

    # Initialisation de Bcrypt
    bcrypt = Bcrypt()
    bcrypt.init_app(app)

    @login_manager.user_loader
    def load_user(user_id):
        return User.query.get(int(user_id))

    # Enregistrement des blueprints
    app.register_blueprint(auth_bp)

    # Création des tables si elles n'existent pas
    with app.app_context():
        db.create_all()

        # Création d'un utilisateur admin par défaut
        admin_user = User.query.filter_by(email='<EMAIL>').first()
        if not admin_user:
            admin_user = User(
                email='<EMAIL>',
                username='admin',
                nom='Administrateur',
                prenom='Système',
                role=UserRole.ADMIN,
                is_active=True,
                is_verified=True
            )
            admin_user.set_password('admin123')
            db.session.add(admin_user)
            db.session.commit()
            print("Utilisateur admin créé: <EMAIL> / admin123")

    return app

app = create_app()

@app.route('/')
def index():
    """Page d'accueil - redirection selon l'authentification"""
    if current_user.is_authenticated:
        return redirect(current_user.get_dashboard_url())
    else:
        return redirect(url_for('auth.welcome'))

@app.route('/dashboard')
@login_required
def dashboard():
    """Tableau de bord principal (pour les utilisateurs connectés)"""
    try:
        # Pour l'instant, utilisons des données de démonstration
        # jusqu'à ce que les modèles soient corrigés
        total_patients = 0
        total_medecins = 0
        total_rdv_aujourd_hui = 0
        rdv_semaine = 0
        rdv_urgents = []
        prochains_rdv = []

        # Date actuelle formatée
        from datetime import datetime
        import locale
        try:
            locale.setlocale(locale.LC_TIME, 'fr_FR.UTF-8')
        except:
            pass  # Si la locale française n'est pas disponible

        date_actuelle = datetime.now().strftime('%A %d %B %Y')

        return render_template('index.html',
                             total_patients=total_patients,
                             total_medecins=total_medecins,
                             total_rdv_aujourd_hui=total_rdv_aujourd_hui,
                             rdv_semaine=rdv_semaine,
                             rdv_urgents=rdv_urgents,
                             prochains_rdv=prochains_rdv,
                             date_actuelle=date_actuelle)
    except Exception as e:
        flash(f'Erreur lors du chargement du tableau de bord: {str(e)}', 'error')
        return render_template('index.html',
                             total_patients=0,
                             total_medecins=0,
                             total_rdv_aujourd_hui=0,
                             rdv_semaine=0,
                             rdv_urgents=[],
                             prochains_rdv=[],
                             date_actuelle=datetime.now().strftime('%A %d %B %Y'))

# ===== DASHBOARDS PAR RÔLE =====

@app.route('/admin/dashboard')
def admin_dashboard():
    """Dashboard administrateur"""
    try:
        # Statistiques pour l'admin
        stats = {
            'total_users': 0,  # User.query.count(),
            'total_medecins': 0,  # User.query.filter_by(role=UserRole.MEDECIN).count(),
            'rdv_aujourd_hui': 0,  # À implémenter
            'revenus_mois': '€0',  # À implémenter
            'users_evolution': {
                'labels': ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Jun'],
                'data': [10, 15, 25, 30, 45, 50]
            },
            'roles_distribution': {
                'labels': ['Patients', 'Médecins', 'Secrétaires', 'Admins'],
                'data': [25, 5, 3, 1]  # Données de démonstration
            }
        }

        recent_activities = []  # À implémenter
        system_alerts = []  # À implémenter

        return render_template('dashboards/admin.html',
                             stats=stats,
                             recent_activities=recent_activities,
                             system_alerts=system_alerts)
    except Exception as e:
        flash(f'Erreur lors du chargement du dashboard: {str(e)}', 'error')
        return render_template('dashboards/admin.html', stats={}, recent_activities=[], system_alerts=[])

@app.route('/medecin/dashboard')
def medecin_dashboard():
    """Dashboard médecin"""
    return render_template('dashboards/medecin.html')

@app.route('/secretaire/dashboard')
def secretaire_dashboard():
    """Dashboard secrétaire"""
    return render_template('dashboards/secretaire.html')

@app.route('/patient/dashboard')
def patient_dashboard():
    """Dashboard patient"""
    return render_template('dashboards/patient.html')

# ===== ROUTES PROTÉGÉES =====

@app.route('/patients')
def patients():
    """Liste des patients"""
    try:
        # Pour l'instant, liste vide jusqu'à correction des modèles
        patients = []
        return render_template('patients/liste.html', patients=patients)
    except Exception as e:
        flash(f'Erreur lors du chargement des patients: {str(e)}', 'error')
        return render_template('patients/liste.html', patients=[])

@app.route('/medecins')
def medecins():
    """Liste des médecins"""
    try:
        # Pour l'instant, liste vide jusqu'à correction des modèles
        medecins = []
        return render_template('medecins/liste.html', medecins=medecins)
    except Exception as e:
        flash(f'Erreur lors du chargement des médecins: {str(e)}', 'error')
        return render_template('medecins/liste.html', medecins=[])

@app.route('/rendez-vous')
def rendez_vous():
    """Liste des rendez-vous"""
    try:
        # Pour l'instant, liste vide jusqu'à correction des modèles
        rdv = []
        return render_template('rendez_vous/liste.html', rendez_vous=rdv)
    except Exception as e:
        flash(f'Erreur lors du chargement des rendez-vous: {str(e)}', 'error')
        return render_template('rendez_vous/liste.html', rendez_vous=[])

@app.route('/planning')
def planning():
    """Planning du jour"""
    try:
        # Pour l'instant, liste vide jusqu'à correction des modèles
        aujourd_hui = date.today()
        rdv_jour = []
        return render_template('rendez_vous/planning.html', rendez_vous=rdv_jour, date=aujourd_hui)
    except Exception as e:
        flash(f'Erreur lors du chargement du planning: {str(e)}', 'error')
        return render_template('rendez_vous/planning.html', rendez_vous=[], date=aujourd_hui)

@app.route('/nouveau-patient')
def nouveau_patient():
    """Formulaire nouveau patient"""
    return render_template('patients/nouveau.html')

@app.route('/nouveau-medecin')
def nouveau_medecin():
    """Formulaire nouveau médecin"""
    return render_template('medecins/nouveau.html')

@app.route('/nouveau-rendez-vous')
def nouveau_rendez_vous():
    """Formulaire nouveau rendez-vous"""
    try:
        # Pour l'instant, listes vides jusqu'à correction des modèles
        patients = []
        medecins = []
        return render_template('rendez_vous/nouveau.html', patients=patients, medecins=medecins)
    except Exception as e:
        flash(f'Erreur lors du chargement du formulaire: {str(e)}', 'error')
        return render_template('rendez_vous/nouveau.html', patients=[], medecins=[])

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)