{% extends "base.html" %}

{% block title %}Mon Dossier Médical - Ilaji Medical{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="display-6 text-info mb-1">
                    <i class="fas fa-user me-3"></i>Mon Dossier Médical
                </h1>
                <p class="text-muted mb-0">Bienvenue dans votre espace personnel de santé</p>
            </div>
            <div class="text-end">
                <div class="badge bg-info fs-6 px-3 py-2">
                    <i class="fas fa-heart me-2"></i>Patient
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Informations personnelles patient -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-0 shadow-sm h-100 card-hover">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-primary bg-gradient rounded-circle p-3">
                            <i class="fas fa-calendar-check fa-2x text-white"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h3 class="mb-1 fw-bold text-primary">{{ stats.prochains_rdv }}</h3>
                        <p class="text-muted mb-0">Prochains RDV</p>
                    </div>
                </div>
            </div>
            <div class="card-footer bg-transparent border-0">
                <a href="/mes-rendez-vous" class="btn btn-outline-primary btn-sm">
                    <i class="fas fa-calendar me-1"></i>Voir planning
                </a>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-0 shadow-sm h-100 card-hover">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-success bg-gradient rounded-circle p-3">
                            <i class="fas fa-prescription-bottle-alt fa-2x text-white"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h3 class="mb-1 fw-bold text-success">{{ stats.prescriptions_actives }}</h3>
                        <p class="text-muted mb-0">Prescriptions actives</p>
                    </div>
                </div>
            </div>
            <div class="card-footer bg-transparent border-0">
                <a href="/mes-prescriptions" class="btn btn-outline-success btn-sm">
                    <i class="fas fa-pills me-1"></i>Voir toutes
                </a>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-0 shadow-sm h-100 card-hover">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-info bg-gradient rounded-circle p-3">
                            <i class="fas fa-file-medical fa-2x text-white"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h3 class="mb-1 fw-bold text-info">{{ stats.resultats_attente }}</h3>
                        <p class="text-muted mb-0">Résultats en attente</p>
                    </div>
                </div>
            </div>
            <div class="card-footer bg-transparent border-0">
                <a href="/mes-resultats" class="btn btn-outline-info btn-sm">
                    <i class="fas fa-vial me-1"></i>Consulter
                </a>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-0 shadow-sm h-100 card-hover">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-warning bg-gradient rounded-circle p-3">
                            <i class="fas fa-exclamation-circle fa-2x text-white"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h3 class="mb-1 fw-bold text-warning">{{ stats.rappels }}</h3>
                        <p class="text-muted mb-0">Rappels importants</p>
                    </div>
                </div>
            </div>
            <div class="card-footer bg-transparent border-0">
                <a href="/mes-rappels" class="btn btn-outline-warning btn-sm">
                    <i class="fas fa-bell me-1"></i>Voir rappels
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Actions rapides patient -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-gradient-info text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-heart me-2"></i>Actions rapides
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="/prendre-rdv" class="btn btn-outline-primary w-100 py-3">
                            <i class="fas fa-calendar-plus fa-2x mb-2 d-block"></i>
                            Prendre RDV
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="/mon-dossier" class="btn btn-outline-success w-100 py-3">
                            <i class="fas fa-folder-open fa-2x mb-2 d-block"></i>
                            Mon dossier complet
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="/mes-documents" class="btn btn-outline-info w-100 py-3">
                            <i class="fas fa-file-download fa-2x mb-2 d-block"></i>
                            Télécharger documents
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="/contact-medecin" class="btn btn-outline-warning w-100 py-3">
                            <i class="fas fa-comments fa-2x mb-2 d-block"></i>
                            Contacter médecin
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Activité récente et alertes -->
<div class="row">
    <div class="col-lg-8 mb-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-history me-2"></i>Activité récente
                </h5>
            </div>
            <div class="card-body">
                {% if recent_activities %}
                    <div class="timeline">
                        {% for activity in recent_activities %}
                        <div class="timeline-item mb-3">
                            <div class="d-flex">
                                <div class="flex-shrink-0">
                                    <div class="bg-{{ activity.type_color }} rounded-circle p-2">
                                        <i class="fas fa-{{ activity.icon }} text-white"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <h6 class="mb-1">{{ activity.title }}</h6>
                                    <p class="text-muted mb-1">{{ activity.description }}</p>
                                    <small class="text-muted">{{ activity.timestamp }}</small>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-history fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Aucune activité récente</h5>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-lg-4 mb-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-header bg-warning text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>Alertes système
                </h5>
            </div>
            <div class="card-body">
                {% if system_alerts %}
                    {% for alert in system_alerts %}
                    <div class="alert alert-{{ alert.level }} alert-dismissible fade show" role="alert">
                        <i class="fas fa-{{ alert.icon }} me-2"></i>
                        <strong>{{ alert.title }}</strong><br>
                        {{ alert.message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                        <h5 class="text-success">Système opérationnel</h5>
                        <p class="text-muted">Aucune alerte active</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Graphiques et statistiques avancées -->
<div class="row">
    <div class="col-lg-6 mb-4">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-gradient-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>Évolution des utilisateurs
                </h5>
            </div>
            <div class="card-body">
                <canvas id="usersChart" height="300"></canvas>
            </div>
        </div>
    </div>
    
    <div class="col-lg-6 mb-4">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-gradient-success text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-pie me-2"></i>Répartition par rôle
                </h5>
            </div>
            <div class="card-body">
                <canvas id="rolesChart" height="300"></canvas>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Graphique d'évolution des utilisateurs
const usersCtx = document.getElementById('usersChart').getContext('2d');
new Chart(usersCtx, {
    type: 'line',
    data: {
        labels: {{ stats.users_evolution.labels | safe }},
        datasets: [{
            label: 'Nouveaux utilisateurs',
            data: {{ stats.users_evolution.data | safe }},
            borderColor: '#2563eb',
            backgroundColor: 'rgba(37, 99, 235, 0.1)',
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// Graphique de répartition par rôle
const rolesCtx = document.getElementById('rolesChart').getContext('2d');
new Chart(rolesCtx, {
    type: 'doughnut',
    data: {
        labels: {{ stats.roles_distribution.labels | safe }},
        datasets: [{
            data: {{ stats.roles_distribution.data | safe }},
            backgroundColor: [
                '#ef4444',
                '#10b981',
                '#f59e0b',
                '#3b82f6'
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

// Actualisation automatique des statistiques
setInterval(function() {
    // TODO: Implémenter l'actualisation AJAX des statistiques
}, 30000); // Toutes les 30 secondes
</script>
{% endblock %}
