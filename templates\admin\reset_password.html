{% extends "base.html" %}

{% block title %}Réinitialiser <PERSON><PERSON> de Passe - Ilaji Medical{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="display-6 text-warning mb-1">
                    <i class="fas fa-key me-3"></i>Réinitialiser Mot de Passe
                </h1>
                <p class="text-muted mb-0">Nouveau mot de passe pour {{ user.prenom }} {{ user.nom }}</p>
            </div>
            <div>
                <a href="/admin/utilisateur/{{ user.id }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Retour aux détails
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-6">
        <!-- Informations utilisateur -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-gradient-info text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user me-2"></i>Utilisateur concerné
                </h5>
            </div>
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="bg-info bg-gradient rounded-circle p-3 me-3">
                        {% if user_type == 'admin' %}
                            <i class="fas fa-user-shield fa-lg text-white"></i>
                        {% elif user_type == 'medecin' %}
                            <i class="fas fa-user-md fa-lg text-white"></i>
                        {% else %}
                            <i class="fas fa-user fa-lg text-white"></i>
                        {% endif %}
                    </div>
                    <div>
                        <h6 class="mb-1">
                            {% if user_type == 'medecin' %}Dr. {% endif %}{{ user.prenom }} {{ user.nom }}
                        </h6>
                        <p class="text-muted mb-1">
                            {% if user_type == 'admin' %}
                                <span class="badge bg-danger">{{ user.role }}</span>
                            {% elif user_type == 'medecin' %}
                                <span class="badge bg-success">Médecin - {{ user.specialite }}</span>
                            {% elif user_type == 'patient' %}
                                <span class="badge bg-info">Patient - CIN: {{ user.cin }}</span>
                            {% else %}
                                <span class="badge bg-secondary">Utilisateur</span>
                            {% endif %}
                        </p>
                        <small class="text-muted">
                            <i class="fas fa-envelope me-1"></i>{{ user.email }}
                        </small>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Formulaire de réinitialisation -->
        <div class="card border-0 shadow-lg">
            <div class="card-header bg-gradient-warning text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-key me-2"></i>Nouveau mot de passe
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" class="needs-validation" novalidate>
                    <div class="alert alert-warning" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Attention :</strong> Cette action va remplacer le mot de passe actuel de l'utilisateur.
                    </div>
                    
                    <div class="mb-3">
                        <label for="nouveau_password" class="form-label">Nouveau mot de passe *</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-lock"></i>
                            </span>
                            <input type="password" class="form-control" id="nouveau_password" name="nouveau_password" 
                                   placeholder="Saisir le nouveau mot de passe" minlength="6" required>
                            <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('nouveau_password')">
                                <i class="fas fa-eye" id="eye-nouveau"></i>
                            </button>
                        </div>
                        <div class="invalid-feedback">
                            Le mot de passe doit contenir au moins 6 caractères.
                        </div>
                        <div class="form-text">
                            <i class="fas fa-info-circle me-1"></i>
                            Le mot de passe doit contenir au moins 6 caractères.
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="confirmer_password" class="form-label">Confirmer le mot de passe *</label>
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-lock"></i>
                            </span>
                            <input type="password" class="form-control" id="confirmer_password" name="confirmer_password" 
                                   placeholder="Confirmer le nouveau mot de passe" minlength="6" required>
                            <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('confirmer_password')">
                                <i class="fas fa-eye" id="eye-confirmer"></i>
                            </button>
                        </div>
                        <div class="invalid-feedback">
                            Veuillez confirmer le mot de passe.
                        </div>
                    </div>
                    
                    <!-- Indicateur de force du mot de passe -->
                    <div class="mb-3">
                        <label class="form-label">Force du mot de passe</label>
                        <div class="progress" style="height: 8px;">
                            <div class="progress-bar" id="password-strength" style="width: 0%"></div>
                        </div>
                        <small class="text-muted" id="password-strength-text">Saisissez un mot de passe</small>
                    </div>
                    
                    <div class="alert alert-info" role="alert">
                        <i class="fas fa-lightbulb me-2"></i>
                        <strong>Conseils de sécurité :</strong>
                        <ul class="mb-0 mt-2">
                            <li>Utilisez au moins 8 caractères</li>
                            <li>Mélangez majuscules et minuscules</li>
                            <li>Incluez des chiffres et symboles</li>
                            <li>Évitez les mots du dictionnaire</li>
                        </ul>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="/admin/utilisateur/{{ user.id }}" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>Annuler
                        </a>
                        <button type="submit" class="btn btn-warning">
                            <i class="fas fa-key me-2"></i>Réinitialiser le mot de passe
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
// Validation Bootstrap
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// Toggle password visibility
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const eyeIcon = document.getElementById('eye-' + fieldId.split('_')[1]);
    
    if (field.type === 'password') {
        field.type = 'text';
        eyeIcon.classList.remove('fa-eye');
        eyeIcon.classList.add('fa-eye-slash');
    } else {
        field.type = 'password';
        eyeIcon.classList.remove('fa-eye-slash');
        eyeIcon.classList.add('fa-eye');
    }
}

// Password strength indicator
document.getElementById('nouveau_password').addEventListener('input', function() {
    const password = this.value;
    const strengthBar = document.getElementById('password-strength');
    const strengthText = document.getElementById('password-strength-text');
    
    let strength = 0;
    let text = '';
    let color = '';
    
    if (password.length >= 6) strength += 20;
    if (password.length >= 8) strength += 20;
    if (/[a-z]/.test(password)) strength += 20;
    if (/[A-Z]/.test(password)) strength += 20;
    if (/[0-9]/.test(password)) strength += 10;
    if (/[^A-Za-z0-9]/.test(password)) strength += 10;
    
    if (strength < 40) {
        text = 'Faible';
        color = 'bg-danger';
    } else if (strength < 70) {
        text = 'Moyen';
        color = 'bg-warning';
    } else {
        text = 'Fort';
        color = 'bg-success';
    }
    
    strengthBar.style.width = strength + '%';
    strengthBar.className = 'progress-bar ' + color;
    strengthText.textContent = text;
});

// Vérification de correspondance des mots de passe
document.getElementById('confirmer_password').addEventListener('input', function() {
    const password = document.getElementById('nouveau_password').value;
    const confirm = this.value;
    
    if (confirm && password !== confirm) {
        this.setCustomValidity('Les mots de passe ne correspondent pas');
    } else {
        this.setCustomValidity('');
    }
});
</script>
{% endblock %}
