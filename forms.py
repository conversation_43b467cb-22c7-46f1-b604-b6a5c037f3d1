from flask_wtf import F<PERSON>kForm
from wtforms import <PERSON><PERSON><PERSON>, PasswordField, SelectField, TextAreaField, BooleanField, SubmitField
from wtforms.validators import DataRequired, Email, Length, EqualTo, ValidationError
from wtforms.fields import <PERSON><PERSON><PERSON>, Time<PERSON>ield, IntegerField, FloatField
from models.user import User, UserRole

class LoginForm(FlaskForm):
    email = StringField('Email', validators=[
        DataRequired(message='L\'email est requis'),
        Email(message='Format d\'email invalide')
    ], render_kw={'placeholder': '<EMAIL>', 'class': 'form-control'})
    
    password = PasswordField('Mot de passe', validators=[
        DataRequired(message='Le mot de passe est requis')
    ], render_kw={'placeholder': 'Votre mot de passe', 'class': 'form-control'})
    
    remember_me = BooleanField('Se souvenir de moi', render_kw={'class': 'form-check-input'})
    submit = SubmitField('Se connecter', render_kw={'class': 'btn btn-primary w-100'})

class RegisterForm(FlaskForm):
    nom = StringField('Nom', validators=[
        DataRequired(message='Le nom est requis'),
        Length(min=2, max=100, message='Le nom doit contenir entre 2 et 100 caractères')
    ], render_kw={'placeholder': 'Votre nom', 'class': 'form-control'})
    
    prenom = StringField('Prénom', validators=[
        DataRequired(message='Le prénom est requis'),
        Length(min=2, max=100, message='Le prénom doit contenir entre 2 et 100 caractères')
    ], render_kw={'placeholder': 'Votre prénom', 'class': 'form-control'})
    
    email = StringField('Email', validators=[
        DataRequired(message='L\'email est requis'),
        Email(message='Format d\'email invalide')
    ], render_kw={'placeholder': '<EMAIL>', 'class': 'form-control'})
    
    username = StringField('Nom d\'utilisateur', validators=[
        DataRequired(message='Le nom d\'utilisateur est requis'),
        Length(min=3, max=80, message='Le nom d\'utilisateur doit contenir entre 3 et 80 caractères')
    ], render_kw={'placeholder': 'nom_utilisateur', 'class': 'form-control'})
    
    telephone = StringField('Téléphone', validators=[
        Length(max=20, message='Le numéro de téléphone ne peut pas dépasser 20 caractères')
    ], render_kw={'placeholder': '+33 1 23 45 67 89', 'class': 'form-control'})
    
    role = SelectField('Rôle', choices=[
        (UserRole.PATIENT.value, 'Patient'),
        (UserRole.SECRETAIRE.value, 'Secrétaire'),
        (UserRole.MEDECIN.value, 'Médecin')
    ], validators=[DataRequired(message='Le rôle est requis')], 
    render_kw={'class': 'form-select'})
    
    password = PasswordField('Mot de passe', validators=[
        DataRequired(message='Le mot de passe est requis'),
        Length(min=8, message='Le mot de passe doit contenir au moins 8 caractères')
    ], render_kw={'placeholder': 'Minimum 8 caractères', 'class': 'form-control'})
    
    password_confirm = PasswordField('Confirmer le mot de passe', validators=[
        DataRequired(message='La confirmation du mot de passe est requise'),
        EqualTo('password', message='Les mots de passe doivent correspondre')
    ], render_kw={'placeholder': 'Répétez votre mot de passe', 'class': 'form-control'})
    
    submit = SubmitField('Créer le compte', render_kw={'class': 'btn btn-primary w-100'})
    
    def validate_email(self, email):
        user = User.query.filter_by(email=email.data).first()
        if user:
            raise ValidationError('Cet email est déjà utilisé. Choisissez un autre email.')
    
    def validate_username(self, username):
        user = User.query.filter_by(username=username.data).first()
        if user:
            raise ValidationError('Ce nom d\'utilisateur est déjà pris. Choisissez un autre nom.')

class ForgotPasswordForm(FlaskForm):
    email = StringField('Email', validators=[
        DataRequired(message='L\'email est requis'),
        Email(message='Format d\'email invalide')
    ], render_kw={'placeholder': '<EMAIL>', 'class': 'form-control'})
    
    submit = SubmitField('Réinitialiser le mot de passe', render_kw={'class': 'btn btn-primary w-100'})

class ResetPasswordForm(FlaskForm):
    password = PasswordField('Nouveau mot de passe', validators=[
        DataRequired(message='Le mot de passe est requis'),
        Length(min=8, message='Le mot de passe doit contenir au moins 8 caractères')
    ], render_kw={'placeholder': 'Minimum 8 caractères', 'class': 'form-control'})
    
    password_confirm = PasswordField('Confirmer le nouveau mot de passe', validators=[
        DataRequired(message='La confirmation du mot de passe est requise'),
        EqualTo('password', message='Les mots de passe doivent correspondre')
    ], render_kw={'placeholder': 'Répétez votre mot de passe', 'class': 'form-control'})
    
    submit = SubmitField('Changer le mot de passe', render_kw={'class': 'btn btn-primary w-100'})

class ChangePasswordForm(FlaskForm):
    current_password = PasswordField('Mot de passe actuel', validators=[
        DataRequired(message='Le mot de passe actuel est requis')
    ], render_kw={'placeholder': 'Votre mot de passe actuel', 'class': 'form-control'})
    
    new_password = PasswordField('Nouveau mot de passe', validators=[
        DataRequired(message='Le nouveau mot de passe est requis'),
        Length(min=8, message='Le mot de passe doit contenir au moins 8 caractères')
    ], render_kw={'placeholder': 'Minimum 8 caractères', 'class': 'form-control'})
    
    new_password_confirm = PasswordField('Confirmer le nouveau mot de passe', validators=[
        DataRequired(message='La confirmation du mot de passe est requise'),
        EqualTo('new_password', message='Les mots de passe doivent correspondre')
    ], render_kw={'placeholder': 'Répétez le nouveau mot de passe', 'class': 'form-control'})
    
    submit = SubmitField('Changer le mot de passe', render_kw={'class': 'btn btn-primary w-100'})

class ProfileForm(FlaskForm):
    nom = StringField('Nom', validators=[
        DataRequired(message='Le nom est requis'),
        Length(min=2, max=100, message='Le nom doit contenir entre 2 et 100 caractères')
    ], render_kw={'class': 'form-control'})
    
    prenom = StringField('Prénom', validators=[
        DataRequired(message='Le prénom est requis'),
        Length(min=2, max=100, message='Le prénom doit contenir entre 2 et 100 caractères')
    ], render_kw={'class': 'form-control'})
    
    email = StringField('Email', validators=[
        DataRequired(message='L\'email est requis'),
        Email(message='Format d\'email invalide')
    ], render_kw={'class': 'form-control'})
    
    telephone = StringField('Téléphone', validators=[
        Length(max=20, message='Le numéro de téléphone ne peut pas dépasser 20 caractères')
    ], render_kw={'class': 'form-control'})
    
    submit = SubmitField('Mettre à jour le profil', render_kw={'class': 'btn btn-primary'})
