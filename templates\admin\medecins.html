{% extends "base.html" %}

{% block title %}Gestion des Médecins - Ilaji Medical{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="display-6 text-success mb-1">
                    <i class="fas fa-user-md me-3"></i>Gestion des Médecins
                </h1>
                <p class="text-muted mb-0">Administration complète du personnel médical</p>
            </div>
            <div>
                <a href="/nouveau-medecin" class="btn btn-success">
                    <i class="fas fa-plus me-2"></i>Nouveau Médecin
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Statistiques médecins -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <i class="fas fa-user-md fa-2x text-success mb-2"></i>
                <h4 class="text-success">{{ medecins|length }}</h4>
                <p class="text-muted mb-0">Total Médecins</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <i class="fas fa-stethoscope fa-2x text-info mb-2"></i>
                <h4 class="text-info">{{ medecins|groupby('specialite')|list|length }}</h4>
                <p class="text-muted mb-0">Spécialités</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <i class="fas fa-euro-sign fa-2x text-warning mb-2"></i>
                <h4 class="text-warning">{{ "%.0f"|format(medecins|sum(attribute='tarif_consultation')/medecins|length if medecins else 0) }}€</h4>
                <p class="text-muted mb-0">Tarif Moyen</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <i class="fas fa-calendar-check fa-2x text-primary mb-2"></i>
                <h4 class="text-primary">0</h4>
                <p class="text-muted mb-0">Consultations Aujourd'hui</p>
            </div>
        </div>
    </div>
</div>

<!-- Liste des médecins -->
<div class="card border-0 shadow-sm">
    <div class="card-header bg-white">
        <h5 class="card-title mb-0">
            <i class="fas fa-list me-2"></i>Liste des Médecins
        </h5>
    </div>
    <div class="card-body">
        {% if medecins %}
        <div class="row">
            {% for medecin in medecins %}
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body">
                        <div class="d-flex align-items-center mb-3">
                            <div class="bg-success bg-gradient rounded-circle p-3 me-3">
                                <i class="fas fa-user-md fa-lg text-white"></i>
                            </div>
                            <div>
                                <h6 class="card-title mb-1">{{ medecin.prenom }} {{ medecin.nom }}</h6>
                                <span class="badge bg-success">{{ medecin.specialite }}</span>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="small text-muted mb-1">
                                <i class="fas fa-envelope me-2"></i>{{ medecin.email }}
                            </div>
                            <div class="small text-muted mb-1">
                                <i class="fas fa-phone me-2"></i>{{ medecin.telephone }}
                            </div>
                            <div class="small text-muted">
                                <i class="fas fa-euro-sign me-2"></i>{{ medecin.tarif_consultation }}€ / consultation
                            </div>
                        </div>
                        
                        {% if medecin.numero_rpps %}
                        <div class="small text-muted mb-2">
                            <strong>RPPS:</strong> {{ medecin.numero_rpps }}
                        </div>
                        {% endif %}
                        
                        {% if medecin.secteur_conventionnement %}
                        <div class="mb-3">
                            <span class="badge bg-info">{{ medecin.secteur_conventionnement }}</span>
                        </div>
                        {% endif %}
                    </div>
                    
                    <div class="card-footer bg-transparent">
                        <div class="btn-group w-100">
                            <a href="/admin/medecin/{{ medecin.id }}" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-eye me-1"></i>Détails
                            </a>
                            <a href="/admin/medecin/{{ medecin.id }}/modifier" class="btn btn-outline-warning btn-sm">
                                <i class="fas fa-edit me-1"></i>Modifier
                            </a>
                            <button class="btn btn-outline-danger btn-sm" onclick="confirmerSuppression({{ medecin.id }}, '{{ medecin.prenom }} {{ medecin.nom }}')">
                                <i class="fas fa-trash me-1"></i>Supprimer
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-user-md fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">Aucun médecin enregistré</h5>
            <p class="text-muted">Commencez par ajouter des médecins à votre équipe.</p>
            <a href="/nouveau-medecin" class="btn btn-success">
                <i class="fas fa-plus me-2"></i>Ajouter un médecin
            </a>
        </div>
        {% endif %}
    </div>
</div>

<script>
function confirmerSuppression(id, nom) {
    if (confirm(`Êtes-vous sûr de vouloir supprimer le médecin "${nom}" ?\n\nCette action est irréversible et supprimera également tous les rendez-vous associés.`)) {
        // Redirection vers la route de suppression
        window.location.href = `/admin/medecin/${id}/supprimer`;
    }
}
</script>
{% endblock %}
