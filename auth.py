from flask import Blueprint, render_template, redirect, url_for, flash, request, session
from flask_login import login_user, logout_user, login_required, current_user
from datetime import datetime
from models.user import User, UserRole, LoginAttempt
from forms import LoginForm, RegisterForm, ForgotPasswordForm, ResetPasswordForm, ChangePasswordForm, ProfileForm
from database import db
import logging

# Configuration du logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Création du blueprint
auth_bp = Blueprint('auth', __name__, url_prefix='/auth')

@auth_bp.route('/welcome')
def welcome():
    """Page d'accueil avec choix de rôle"""
    if current_user.is_authenticated:
        return redirect(current_user.get_dashboard_url())
    return render_template('auth/welcome.html')

@auth_bp.route('/login', methods=['GET', 'POST'])
def login():
    """Page de connexion"""
    if current_user.is_authenticated:
        return redirect(current_user.get_dashboard_url())

    # Récupération du rôle depuis l'URL ou le formulaire
    role = request.args.get('role') or request.form.get('role')

    form = LoginForm()
    
    if form.validate_on_submit():
        # Recherche de l'utilisateur
        user = User.query.filter_by(email=form.email.data).first()
        
        # Enregistrement de la tentative de connexion
        attempt = LoginAttempt(
            email=form.email.data,
            ip_address=request.remote_addr,
            success=False,
            user_agent=request.headers.get('User-Agent', '')
        )
        
        if user and user.check_password(form.password.data):
            if not user.is_active:
                flash('Votre compte a été désactivé. Contactez l\'administrateur.', 'error')
                db.session.add(attempt)
                db.session.commit()
                return render_template('auth/login.html', form=form, role=role)

            # Vérification du rôle si spécifié
            if role and user.role.value != role:
                flash(f'Ce compte n\'est pas autorisé pour le rôle {role}.', 'error')
                db.session.add(attempt)
                db.session.commit()
                return render_template('auth/login.html', form=form, role=role)

            # Connexion réussie
            attempt.success = True
            user.derniere_connexion = datetime.utcnow()

            login_user(user, remember=form.remember_me.data)

            db.session.add(attempt)
            db.session.commit()

            logger.info(f'Connexion réussie pour {user.email} (rôle: {user.role.value})')
            flash(f'Bienvenue, {user.nom_complet} !', 'success')

            # Redirection selon le rôle
            next_page = request.args.get('next')
            if next_page:
                return redirect(next_page)
            else:
                return redirect(user.get_dashboard_url())
        else:
            flash('Email ou mot de passe incorrect.', 'error')
            db.session.add(attempt)
            db.session.commit()
            logger.warning(f'Tentative de connexion échouée pour {form.email.data}')

    return render_template('auth/login.html', form=form, role=role)

@auth_bp.route('/register', methods=['GET', 'POST'])
def register():
    """Page d'inscription"""
    if current_user.is_authenticated:
        return redirect(current_user.get_dashboard_url())

    # Récupération du rôle depuis l'URL ou le formulaire
    role = request.args.get('role') or request.form.get('role')

    form = RegisterForm()

    # Pré-remplir le rôle si spécifié
    if role and request.method == 'GET':
        form.role.data = role
    
    if form.validate_on_submit():
        try:
            # Utiliser le rôle du formulaire ou celui spécifié dans l'URL
            user_role = role if role else form.role.data

            # Création du nouvel utilisateur
            user = User(
                email=form.email.data,
                username=form.username.data,
                nom=form.nom.data,
                prenom=form.prenom.data,
                telephone=form.telephone.data,
                role=UserRole(user_role),
                is_active=True,
                is_verified=False  # Nécessite une vérification email
            )
            user.set_password(form.password.data)
            
            db.session.add(user)
            db.session.commit()
            
            logger.info(f'Nouveau compte créé: {user.email} (rôle: {user.role.value})')
            flash('Votre compte a été créé avec succès ! Vous pouvez maintenant vous connecter.', 'success')
            
            return redirect(url_for('auth.login'))
            
        except Exception as e:
            db.session.rollback()
            logger.error(f'Erreur lors de la création du compte: {str(e)}')
            flash('Une erreur est survenue lors de la création du compte. Veuillez réessayer.', 'error')

    return render_template('auth/register.html', form=form, role=role)

@auth_bp.route('/logout')
@login_required
def logout():
    """Déconnexion"""
    user_name = current_user.nom_complet
    logger.info(f'Déconnexion de {current_user.email}')
    logout_user()
    flash(f'Au revoir, {user_name} ! Vous avez été déconnecté.', 'info')
    return redirect(url_for('auth.login'))

@auth_bp.route('/profile', methods=['GET', 'POST'])
@login_required
def profile():
    """Page de profil utilisateur"""
    form = ProfileForm(obj=current_user)
    
    if form.validate_on_submit():
        try:
            # Vérification si l'email est déjà utilisé par un autre utilisateur
            if form.email.data != current_user.email:
                existing_user = User.query.filter_by(email=form.email.data).first()
                if existing_user:
                    flash('Cet email est déjà utilisé par un autre compte.', 'error')
                    return render_template('auth/profile.html', form=form)
            
            # Mise à jour des informations
            current_user.nom = form.nom.data
            current_user.prenom = form.prenom.data
            current_user.email = form.email.data
            current_user.telephone = form.telephone.data
            
            db.session.commit()
            
            logger.info(f'Profil mis à jour pour {current_user.email}')
            flash('Votre profil a été mis à jour avec succès.', 'success')
            
        except Exception as e:
            db.session.rollback()
            logger.error(f'Erreur lors de la mise à jour du profil: {str(e)}')
            flash('Une erreur est survenue lors de la mise à jour.', 'error')
    
    return render_template('auth/profile.html', form=form)

@auth_bp.route('/change-password', methods=['GET', 'POST'])
@login_required
def change_password():
    """Changement de mot de passe"""
    form = ChangePasswordForm()
    
    if form.validate_on_submit():
        if not current_user.check_password(form.current_password.data):
            flash('Le mot de passe actuel est incorrect.', 'error')
            return render_template('auth/change_password.html', form=form)
        
        try:
            current_user.set_password(form.new_password.data)
            db.session.commit()
            
            logger.info(f'Mot de passe changé pour {current_user.email}')
            flash('Votre mot de passe a été changé avec succès.', 'success')
            
            return redirect(url_for('auth.profile'))
            
        except Exception as e:
            db.session.rollback()
            logger.error(f'Erreur lors du changement de mot de passe: {str(e)}')
            flash('Une erreur est survenue lors du changement de mot de passe.', 'error')
    
    return render_template('auth/change_password.html', form=form)

@auth_bp.route('/forgot-password', methods=['GET', 'POST'])
def forgot_password():
    """Mot de passe oublié"""
    if current_user.is_authenticated:
        return redirect(current_user.get_dashboard_url())
    
    form = ForgotPasswordForm()
    
    if form.validate_on_submit():
        user = User.query.filter_by(email=form.email.data).first()
        if user:
            # TODO: Implémenter l'envoi d'email de réinitialisation
            logger.info(f'Demande de réinitialisation de mot de passe pour {user.email}')
            flash('Si cette adresse email existe dans notre système, vous recevrez un lien de réinitialisation.', 'info')
        else:
            # Ne pas révéler si l'email existe ou non
            flash('Si cette adresse email existe dans notre système, vous recevrez un lien de réinitialisation.', 'info')
        
        return redirect(url_for('auth.login'))
    
    return render_template('auth/forgot_password.html', form=form)

@auth_bp.route('/verify-email')
@login_required
def verify_email():
    """Page de vérification d'email"""
    if current_user.is_verified:
        flash('Votre email est déjà vérifié.', 'info')
        return redirect(current_user.get_dashboard_url())
    
    # TODO: Implémenter l'envoi d'email de vérification
    return render_template('auth/verify_email.html')

@auth_bp.route('/dashboard')
@login_required
def dashboard():
    """Redirection vers le dashboard approprié selon le rôle"""
    return redirect(current_user.get_dashboard_url())

# Gestionnaire d'erreurs pour le blueprint
@auth_bp.errorhandler(403)
def forbidden(error):
    flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'error')
    return redirect(url_for('auth.login'))

@auth_bp.errorhandler(404)
def not_found(error):
    flash('Page non trouvée.', 'error')
    return redirect(url_for('auth.login'))
