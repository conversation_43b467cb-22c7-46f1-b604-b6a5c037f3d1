{% extends "base.html" %}

{% block title %}Détails Patient - Ilaji Medical{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="display-6 text-info mb-1">
                    <i class="fas fa-user me-3"></i>Détails Patient
                </h1>
                <p class="text-muted mb-0">Dossier médical de {{ patient.prenom }} {{ patient.nom }}</p>
            </div>
            <div>
                <a href="/admin/utilisateurs" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Retour à la liste
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Informations principales -->
    <div class="col-lg-8">
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-gradient-info text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user me-2"></i>Informations personnelles
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted">Prénom</label>
                            <p class="fw-bold">{{ patient.prenom }}</p>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label text-muted">Nom</label>
                            <p class="fw-bold">{{ patient.nom }}</p>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label text-muted">CIN</label>
                            <p class="fw-bold">
                                <i class="fas fa-id-card me-2 text-primary"></i>
                                {{ patient.cin }}
                            </p>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label text-muted">Date de naissance</label>
                            <p class="fw-bold">
                                <i class="fas fa-birthday-cake me-2 text-warning"></i>
                                {{ patient.date_naissance if patient.date_naissance else 'Non renseignée' }}
                            </p>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted">Email</label>
                            <p class="fw-bold">
                                <i class="fas fa-envelope me-2 text-primary"></i>
                                <a href="mailto:{{ patient.email }}" class="text-decoration-none">{{ patient.email }}</a>
                            </p>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label text-muted">Téléphone</label>
                            <p class="fw-bold">
                                <i class="fas fa-phone me-2 text-success"></i>
                                <a href="tel:{{ patient.telephone }}" class="text-decoration-none">{{ patient.telephone }}</a>
                            </p>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label text-muted">Profession</label>
                            <p class="fw-bold">
                                <i class="fas fa-briefcase me-2 text-info"></i>
                                {{ patient.profession if patient.profession else 'Non renseignée' }}
                            </p>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label text-muted">Adresse</label>
                            <p class="fw-bold">
                                <i class="fas fa-map-marker-alt me-2 text-danger"></i>
                                {{ patient.adresse if patient.adresse else 'Non renseignée' }}
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Historique médical -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-notes-medical me-2"></i>Historique médical
                </h5>
            </div>
            <div class="card-body">
                <div class="text-center py-4">
                    <i class="fas fa-notes-medical fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">Aucun historique médical</h5>
                    <p class="text-muted">L'historique des consultations apparaîtra ici une fois que le patient aura des rendez-vous.</p>
                    <a href="/nouveau-rendez-vous?patient={{ patient.id }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Programmer un rendez-vous
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Actions rapides -->
    <div class="col-lg-4">
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-tools me-2"></i>Actions rapides
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="/admin/patient/{{ patient.id }}/modifier" class="btn btn-warning">
                        <i class="fas fa-edit me-2"></i>Modifier les informations
                    </a>
                    
                    <a href="/nouveau-rendez-vous?patient={{ patient.id }}" class="btn btn-success">
                        <i class="fas fa-calendar-plus me-2"></i>Nouveau rendez-vous
                    </a>
                    
                    <button class="btn btn-info" onclick="alert('Fonctionnalité en cours de développement')">
                        <i class="fas fa-file-medical me-2"></i>Dossier médical
                    </button>
                    
                    <button class="btn btn-secondary" onclick="alert('Fonctionnalité en cours de développement')">
                        <i class="fas fa-prescription me-2"></i>Prescriptions
                    </button>
                    
                    <hr>
                    
                    <button class="btn btn-outline-danger" onclick="confirmerSuppression({{ patient.id }}, '{{ patient.prenom }} {{ patient.nom }}')">
                        <i class="fas fa-trash me-2"></i>Supprimer le patient
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Statistiques patient -->
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>Statistiques
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6 mb-3">
                        <div class="border-end">
                            <h4 class="text-primary mb-1">0</h4>
                            <small class="text-muted">Consultations</small>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <h4 class="text-success mb-1">0</h4>
                        <small class="text-muted">Prescriptions</small>
                    </div>
                    <div class="col-6">
                        <div class="border-end">
                            <h4 class="text-warning mb-1">0</h4>
                            <small class="text-muted">RDV à venir</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h4 class="text-info mb-1">0€</h4>
                        <small class="text-muted">Total payé</small>
                    </div>
                </div>
                
                <hr>
                
                <div class="text-center">
                    <small class="text-muted">
                        <i class="fas fa-user-plus me-1"></i>
                        Patient depuis : {{ patient.date_creation if patient.date_creation else 'Récemment' }}
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function confirmerSuppression(id, nom) {
    if (confirm(`Êtes-vous sûr de vouloir supprimer le patient "${nom}" ?\n\nCette action supprimera également tout son historique médical.`)) {
        window.location.href = `/admin/patient/${id}/supprimer`;
    }
}
</script>
{% endblock %}
