{% extends "base.html" %}

{% block title %}<PERSON><PERSON><PERSON>tilisa<PERSON>ur - <PERSON><PERSON><PERSON>{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="display-6 text-primary mb-1">
                    <i class="fas fa-user-plus me-3"></i><PERSON><PERSON>er un Utilisateur
                </h1>
                <p class="text-muted mb-0">Ajouter un nouveau médecin, patient ou sec<PERSON>taire</p>
            </div>
            <div>
                <a href="/admin/dashboard" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Retour Dashboard
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-10">
        <!-- Sélection du type d'utilisateur -->
        <div class="card border-0 shadow-lg mb-4">
            <div class="card-header bg-gradient-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-users me-2"></i>Type d'utilisateur
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <a href="/nouveau-medecin" class="card h-100 text-decoration-none border-success">
                            <div class="card-body text-center">
                                <i class="fas fa-user-md fa-3x text-success mb-3"></i>
                                <h5 class="card-title text-success">Médecin</h5>
                                <p class="card-text text-muted">Ajouter un nouveau médecin avec spécialité et tarifs</p>
                                <span class="btn btn-success">
                                    <i class="fas fa-plus me-2"></i>Créer Médecin
                                </span>
                            </div>
                        </a>
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <a href="/nouveau-patient" class="card h-100 text-decoration-none border-info">
                            <div class="card-body text-center">
                                <i class="fas fa-user fa-3x text-info mb-3"></i>
                                <h5 class="card-title text-info">Patient</h5>
                                <p class="card-text text-muted">Enregistrer un nouveau patient avec CIN et informations</p>
                                <span class="btn btn-info">
                                    <i class="fas fa-plus me-2"></i>Créer Patient
                                </span>
                            </div>
                        </a>
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <div class="card h-100 border-warning">
                            <div class="card-body text-center">
                                <i class="fas fa-user-tie fa-3x text-warning mb-3"></i>
                                <h5 class="card-title text-warning">Secrétaire</h5>
                                <p class="card-text text-muted">Ajouter un membre du personnel administratif</p>
                                <button class="btn btn-warning" onclick="alert('Fonctionnalité en cours de développement')">
                                    <i class="fas fa-plus me-2"></i>Créer Secrétaire
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Statistiques utilisateurs -->
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>Statistiques des utilisateurs
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="text-center">
                            <i class="fas fa-user-md fa-2x text-success mb-2"></i>
                            <h4 class="text-success">{{ medecins|length if medecins is defined else 0 }}</h4>
                            <p class="text-muted">Médecins</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <i class="fas fa-user fa-2x text-info mb-2"></i>
                            <h4 class="text-info">{{ patients|length if patients is defined else 0 }}</h4>
                            <p class="text-muted">Patients</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <i class="fas fa-user-tie fa-2x text-warning mb-2"></i>
                            <h4 class="text-warning">2</h4>
                            <p class="text-muted">Secrétaires</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
