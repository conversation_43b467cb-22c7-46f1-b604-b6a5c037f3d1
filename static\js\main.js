﻿// Ilaji Medical - JavaScript Avancé

document.addEventListener('DOMContentLoaded', function() {
    // Initialisation de l'application
    initializeApp();
    
    // Animation d'apparition pour les éléments
    animateElements();
    
    // Gestion des formulaires
    setupFormValidation();
    
    // Gestion des notifications
    setupNotifications();
    
    // Mise à jour de l'heure
    updateTimeDisplay();
    
    // Gestion des modales
    setupModals();
    
    console.log(' Ilaji Medical - Application initialisée avec succès');
});

// Initialisation de l'application
function initializeApp() {
    // Configuration des tooltips Bootstrap
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Configuration des popovers Bootstrap
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
    
    // Gestion des liens actifs dans la navigation
    highlightActiveNavLink();
}

// Animation des éléments au chargement
function animateElements() {
    const cards = document.querySelectorAll('.card');
    const observer = new IntersectionObserver((entries) => {
        entries.forEach((entry, index) => {
            if (entry.isIntersecting) {
                setTimeout(() => {
                    entry.target.classList.add('fade-in-up');
                }, index * 100);
                observer.unobserve(entry.target);
            }
        });
    }, { threshold: 0.1 });
    
    cards.forEach(card => {
        observer.observe(card);
    });
}

// Validation des formulaires
function setupFormValidation() {
    const forms = document.querySelectorAll('form');
    
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            if (!validateForm(this)) {
                e.preventDefault();
                e.stopPropagation();
            }
            this.classList.add('was-validated');
        });
        
        // Validation en temps réel
        const inputs = form.querySelectorAll('input, select, textarea');
        inputs.forEach(input => {
            input.addEventListener('blur', function() {
                validateField(this);
            });
            
            input.addEventListener('input', function() {
                if (this.classList.contains('is-invalid')) {
                    validateField(this);
                }
            });
        });
    });
}

// Validation d'un champ
function validateField(field) {
    const value = field.value.trim();
    const isRequired = field.hasAttribute('required');
    const type = field.type;
    
    // Supprimer les classes de validation précédentes
    field.classList.remove('is-valid', 'is-invalid');
    
    // Validation des champs requis
    if (isRequired && !value) {
        field.classList.add('is-invalid');
        showFieldError(field, 'Ce champ est obligatoire');
        return false;
    }
    
    // Validation spécifique par type
    if (value) {
        switch (type) {
            case 'email':
                if (!isValidEmail(value)) {
                    field.classList.add('is-invalid');
                    showFieldError(field, 'Adresse email invalide');
                    return false;
                }
                break;
            case 'tel':
                if (!isValidPhone(value)) {
                    field.classList.add('is-invalid');
                    showFieldError(field, 'Numéro de téléphone invalide');
                    return false;
                }
                break;
            case 'date':
                if (!isValidDate(value)) {
                    field.classList.add('is-invalid');
                    showFieldError(field, 'Date invalide');
                    return false;
                }
                break;
        }
    }
    
    field.classList.add('is-valid');
    hideFieldError(field);
    return true;
}

// Validation d'un formulaire complet
function validateForm(form) {
    const fields = form.querySelectorAll('input[required], select[required], textarea[required]');
    let isValid = true;
    
    fields.forEach(field => {
        if (!validateField(field)) {
            isValid = false;
        }
    });
    
    return isValid;
}

// Afficher une erreur de champ
function showFieldError(field, message) {
    let errorDiv = field.parentNode.querySelector('.invalid-feedback');
    if (!errorDiv) {
        errorDiv = document.createElement('div');
        errorDiv.className = 'invalid-feedback';
        field.parentNode.appendChild(errorDiv);
    }
    errorDiv.textContent = message;
}

// Masquer une erreur de champ
function hideFieldError(field) {
    const errorDiv = field.parentNode.querySelector('.invalid-feedback');
    if (errorDiv) {
        errorDiv.remove();
    }
}

// Validation email
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Validation téléphone
function isValidPhone(phone) {
    const phoneRegex = /^(?:(?:\+|00)33|0)\s*[1-9](?:[\s.-]*\d{2}){4}$/;
    return phoneRegex.test(phone);
}

// Validation date
function isValidDate(dateString) {
    const date = new Date(dateString);
    return date instanceof Date && !isNaN(date);
}

// Gestion des notifications
function setupNotifications() {
    // Auto-masquage des alertes après 5 secondes
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
        setTimeout(() => {
            if (alert.parentNode) {
                alert.style.transition = 'opacity 0.5s ease';
                alert.style.opacity = '0';
                setTimeout(() => {
                    if (alert.parentNode) {
                        alert.remove();
                    }
                }, 500);
            }
        }, 5000);
    });
}

// Afficher une notification
function showNotification(message, type = 'info', duration = 5000) {
    const alertContainer = document.querySelector('.container');
    const alert = document.createElement('div');
    alert.className = lert alert- alert-dismissible fade show mt-3;
    alert.innerHTML = 
        <i class="fas fa- me-2"></i>
        
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    ;
    
    alertContainer.insertBefore(alert, alertContainer.firstChild);
    
    // Auto-masquage
    setTimeout(() => {
        if (alert.parentNode) {
            alert.style.transition = 'opacity 0.5s ease';
            alert.style.opacity = '0';
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.remove();
                }
            }, 500);
        }
    }, duration);
}

// Obtenir l'icône pour un type de notification
function getIconForType(type) {
    const icons = {
        'success': 'check-circle',
        'danger': 'exclamation-triangle',
        'warning': 'exclamation-circle',
        'info': 'info-circle'
    };
    return icons[type] || 'info-circle';
}

// Mise à jour de l'affichage de l'heure
function updateTimeDisplay() {
    function updateTime() {
        const now = new Date();
        const timeString = now.toLocaleTimeString('fr-FR');
        const dateString = now.toLocaleDateString('fr-FR', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
        
        const timeElement = document.getElementById('current-time');
        if (timeElement) {
            timeElement.textContent = timeString;
        }
        
        const dateElements = document.querySelectorAll('.current-date');
        dateElements.forEach(element => {
            element.textContent = dateString;
        });
    }
    
    updateTime();
    setInterval(updateTime, 1000);
}

// Gestion des modales
function setupModals() {
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        modal.addEventListener('show.bs.modal', function() {
            // Focus sur le premier champ de saisie
            const firstInput = modal.querySelector('input, select, textarea');
            if (firstInput) {
                setTimeout(() => firstInput.focus(), 100);
            }
        });
        
        modal.addEventListener('hidden.bs.modal', function() {
            // Réinitialiser le formulaire
            const form = modal.querySelector('form');
            if (form) {
                form.reset();
                form.classList.remove('was-validated');
                
                // Supprimer les classes de validation
                const fields = form.querySelectorAll('.is-valid, .is-invalid');
                fields.forEach(field => {
                    field.classList.remove('is-valid', 'is-invalid');
                });
            }
        });
    });
}

// Mettre en évidence le lien de navigation actif
function highlightActiveNavLink() {
    const currentPath = window.location.pathname;
    const navLinks = document.querySelectorAll('.navbar-nav .nav-link');
    
    navLinks.forEach(link => {
        const href = link.getAttribute('href');
        if (href && currentPath.startsWith(href) && href !== '/') {
            link.classList.add('active');
        } else if (href === '/' && currentPath === '/') {
            link.classList.add('active');
        }
    });
}

// Formatage automatique des champs
function setupFieldFormatting() {
    // Formatage des numéros de téléphone
    const phoneInputs = document.querySelectorAll('input[type="tel"]');
    phoneInputs.forEach(input => {
        input.addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            if (value.length >= 10) {
                value = value.substring(0, 10);
                value = value.replace(/(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})/, '....');
            }
            e.target.value = value;
        });
    });
    
    // Formatage des numéros de sécurité sociale
    const ssnInputs = document.querySelectorAll('input[name="numero_securite_sociale"]');
    ssnInputs.forEach(input => {
        input.addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            if (value.length > 15) {
                value = value.substring(0, 15);
            }
            e.target.value = value;
        });
    });
}

// Gestion des changements de statut de rendez-vous
function changerStatutRendezVous(rdvId, nouveauStatut) {
    fetch(/rendez-vous//statut, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ statut: nouveauStatut })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');
            // Recharger la page ou mettre à jour l'interface
            location.reload();
        } else {
            showNotification(data.message, 'danger');
        }
    })
    .catch(error => {
        console.error('Erreur:', error);
        showNotification('Erreur lors de la mise à jour du statut', 'danger');
    });
}

// Recherche en temps réel
function setupLiveSearch() {
    const searchInputs = document.querySelectorAll('.search-input');
    searchInputs.forEach(input => {
        let searchTimeout;
        
        input.addEventListener('input', function(e) {
            clearTimeout(searchTimeout);
            const searchTerm = e.target.value.toLowerCase();
            
            searchTimeout = setTimeout(() => {
                const table = document.querySelector('.table tbody');
                if (table) {
                    const rows = table.querySelectorAll('tr');
                    
                    rows.forEach(row => {
                        const text = row.textContent.toLowerCase();
                        if (text.includes(searchTerm)) {
                            row.style.display = '';
                            row.style.animation = 'fadeInUp 0.3s ease';
                        } else {
                            row.style.display = 'none';
                        }
                    });
                }
            }, 300);
        });
    });
}

// Sauvegarde automatique des brouillons
function setupAutoSave() {
    const formInputs = document.querySelectorAll('form input, form select, form textarea');
    formInputs.forEach(input => {
        const storageKey = ilaji_draft__;
        
        // Charger le brouillon
        const savedValue = localStorage.getItem(storageKey);
        if (savedValue && !input.value) {
            input.value = savedValue;
        }
        
        // Sauvegarder les modifications
        input.addEventListener('input', function() {
            localStorage.setItem(storageKey, input.value);
        });
        
        // Nettoyer le brouillon lors de la soumission
        const form = input.closest('form');
        if (form) {
            form.addEventListener('submit', function() {
                localStorage.removeItem(storageKey);
            });
        }
    });
}

// Fonctions utilitaires globales
window.IlajiMedical = {
    // Fonction pour confirmer une action
    confirm: function(message, callback) {
        if (confirm(message)) {
            callback();
        }
    },
    
    // Fonction pour formater une date
    formatDate: function(date) {
        return new Date(date).toLocaleDateString('fr-FR');
    },
    
    // Fonction pour formater une heure
    formatTime: function(date) {
        return new Date(date).toLocaleTimeString('fr-FR', {
            hour: '2-digit',
            minute: '2-digit'
        });
    },
    
    // Fonction pour calculer l'âge
    calculateAge: function(birthDate) {
        const today = new Date();
        const birth = new Date(birthDate);
        let age = today.getFullYear() - birth.getFullYear();
        const monthDiff = today.getMonth() - birth.getMonth();
        
        if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
            age--;
        }
        
        return age;
    },
    
    // Fonction pour afficher une notification
    showNotification: showNotification,
    
    // Fonction pour changer le statut d'un rendez-vous
    changerStatutRendezVous: changerStatutRendezVous
};

// Initialiser les fonctionnalités supplémentaires
document.addEventListener('DOMContentLoaded', function() {
    setupFieldFormatting();
    setupLiveSearch();
    setupAutoSave();
});
