{% extends "base.html" %}

{% block title %}Modifier Médecin - Ilaji Medical{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="display-6 text-warning mb-1">
                    <i class="fas fa-edit me-3"></i>Modifier Médecin
                </h1>
                <p class="text-muted mb-0">Modification des informations de {{ medecin.prenom }} {{ medecin.nom }}</p>
            </div>
            <div>
                <a href="/admin/medecins" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Retour à la liste
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card border-0 shadow-lg">
            <div class="card-header bg-gradient-warning text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user-md me-2"></i>Informations du médecin
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" class="needs-validation" novalidate>
                    <div class="row">
                        <!-- Informations personnelles -->
                        <div class="col-md-6">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-user me-2"></i>Informations personnelles
                            </h6>
                            
                            <div class="mb-3">
                                <label for="prenom" class="form-label">Prénom *</label>
                                <input type="text" class="form-control" id="prenom" name="prenom" 
                                       value="{{ medecin.prenom }}" required>
                                <div class="invalid-feedback">
                                    Veuillez saisir le prénom.
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="nom" class="form-label">Nom *</label>
                                <input type="text" class="form-control" id="nom" name="nom" 
                                       value="{{ medecin.nom }}" required>
                                <div class="invalid-feedback">
                                    Veuillez saisir le nom.
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="telephone" class="form-label">Téléphone</label>
                                <input type="tel" class="form-control" id="telephone" name="telephone" 
                                       value="{{ medecin.telephone }}" placeholder="01.23.45.67.89">
                            </div>
                            
                            <div class="mb-3">
                                <label for="email" class="form-label">Email</label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="{{ medecin.email }}" placeholder="<EMAIL>">
                            </div>
                        </div>
                        
                        <!-- Informations professionnelles -->
                        <div class="col-md-6">
                            <h6 class="text-success mb-3">
                                <i class="fas fa-stethoscope me-2"></i>Informations professionnelles
                            </h6>
                            
                            <div class="mb-3">
                                <label for="specialite" class="form-label">Spécialité *</label>
                                <select class="form-select" id="specialite" name="specialite" required>
                                    <option value="">Choisir une spécialité</option>
                                    <option value="Médecine générale" {{ 'selected' if medecin.specialite == 'Médecine générale' }}>Médecine générale</option>
                                    <option value="Cardiologie" {{ 'selected' if medecin.specialite == 'Cardiologie' }}>Cardiologie</option>
                                    <option value="Dermatologie" {{ 'selected' if medecin.specialite == 'Dermatologie' }}>Dermatologie</option>
                                    <option value="Pédiatrie" {{ 'selected' if medecin.specialite == 'Pédiatrie' }}>Pédiatrie</option>
                                    <option value="Gynécologie" {{ 'selected' if medecin.specialite == 'Gynécologie' }}>Gynécologie</option>
                                    <option value="Orthopédie" {{ 'selected' if medecin.specialite == 'Orthopédie' }}>Orthopédie</option>
                                    <option value="Ophtalmologie" {{ 'selected' if medecin.specialite == 'Ophtalmologie' }}>Ophtalmologie</option>
                                    <option value="ORL" {{ 'selected' if medecin.specialite == 'ORL' }}>ORL</option>
                                    <option value="Psychiatrie" {{ 'selected' if medecin.specialite == 'Psychiatrie' }}>Psychiatrie</option>
                                    <option value="Radiologie" {{ 'selected' if medecin.specialite == 'Radiologie' }}>Radiologie</option>
                                </select>
                                <div class="invalid-feedback">
                                    Veuillez choisir une spécialité.
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="tarif_consultation" class="form-label">Tarif consultation (€)</label>
                                <input type="number" class="form-control" id="tarif_consultation" name="tarif_consultation" 
                                       value="{{ medecin.tarif_consultation }}" step="0.01" min="0" placeholder="25.00">
                            </div>
                            
                            <div class="mb-3">
                                <label for="numero_rpps" class="form-label">Numéro RPPS</label>
                                <input type="text" class="form-control" id="numero_rpps" name="numero_rpps" 
                                       value="{{ medecin.numero_rpps }}" placeholder="12345678901">
                            </div>
                            
                            <div class="mb-3">
                                <label for="numero_adeli" class="form-label">Numéro ADELI</label>
                                <input type="text" class="form-control" id="numero_adeli" name="numero_adeli" 
                                       value="{{ medecin.numero_adeli }}" placeholder="987654321">
                            </div>
                        </div>
                    </div>
                    
                    <!-- Informations complémentaires -->
                    <div class="row">
                        <div class="col-12">
                            <h6 class="text-info mb-3">
                                <i class="fas fa-info-circle me-2"></i>Informations complémentaires
                            </h6>
                            
                            <div class="mb-3">
                                <label for="secteur_conventionnement" class="form-label">Secteur de conventionnement</label>
                                <select class="form-select" id="secteur_conventionnement" name="secteur_conventionnement">
                                    <option value="">Non spécifié</option>
                                    <option value="Secteur 1" {{ 'selected' if medecin.secteur_conventionnement == 'Secteur 1' }}>Secteur 1</option>
                                    <option value="Secteur 2" {{ 'selected' if medecin.secteur_conventionnement == 'Secteur 2' }}>Secteur 2</option>
                                    <option value="Non conventionné" {{ 'selected' if medecin.secteur_conventionnement == 'Non conventionné' }}>Non conventionné</option>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label for="adresse" class="form-label">Adresse du cabinet</label>
                                <textarea class="form-control" id="adresse" name="adresse" rows="3" 
                                          placeholder="Adresse complète du cabinet médical">{{ medecin.adresse }}</textarea>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Boutons d'action -->
                    <div class="row">
                        <div class="col-12">
                            <div class="d-flex justify-content-between">
                                <a href="/admin/medecins" class="btn btn-secondary">
                                    <i class="fas fa-times me-2"></i>Annuler
                                </a>
                                <button type="submit" class="btn btn-warning">
                                    <i class="fas fa-save me-2"></i>Enregistrer les modifications
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
// Validation Bootstrap
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>
{% endblock %}
