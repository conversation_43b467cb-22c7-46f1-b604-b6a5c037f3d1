{% extends "auth/base_auth.html" %}

{% block title %}Inscription - Ilaji Medical{% endblock %}

{% block auth_title %}
    {% if role %}
        Créer un compte {{ role.title() }}
    {% else %}
        Créer un compte
    {% endif %}
{% endblock %}
{% block auth_subtitle %}
    {% if role %}
        Rejoignez Ilaji Medical en tant que {{ role }}
    {% else %}
        Rejoignez notre plateforme médicale
    {% endif %}
{% endblock %}

{% block auth_content %}
{% if role %}
<div class="role-indicator mb-4">
    <div class="d-flex align-items-center justify-content-center p-3 rounded-3"
         style="background: linear-gradient(135deg,
         {% if role == 'admin' %}#ef4444{% elif role == 'medecin' %}#10b981{% elif role == 'secretaire' %}#f59e0b{% else %}#06b6d4{% endif %} 0%,
         {% if role == 'admin' %}#dc2626{% elif role == 'medecin' %}#059669{% elif role == 'secretaire' %}#d97706{% else %}#0891b2{% endif %} 100%); color: white;">
        <i class="fas fa-{% if role == 'admin' %}user-shield{% elif role == 'medecin' %}user-md{% elif role == 'secretaire' %}user-tie{% else %}user{% endif %} fa-2x me-3"></i>
        <div>
            <h5 class="mb-0">Inscription {{ role.title() }}</h5>
            <small class="opacity-75">
                {% if role == 'admin' %}Administrateur système
                {% elif role == 'medecin' %}Professionnel de santé
                {% elif role == 'secretaire' %}Personnel administratif
                {% else %}Espace patient{% endif %}
            </small>
        </div>
    </div>
    <div class="text-center mt-3">
        <a href="{{ url_for('auth.welcome') }}" class="btn btn-outline-secondary btn-sm">
            <i class="fas fa-arrow-left me-1"></i>Changer de rôle
        </a>
    </div>
</div>
{% endif %}

<form method="POST" novalidate>
    {{ form.hidden_tag() }}
    {% if role %}
        <input type="hidden" name="role" value="{{ role }}">
    {% endif %}
    
    <div class="row">
        <div class="col-md-6">
            <div class="form-floating">
                {{ form.prenom(class="form-control" + (" is-invalid" if form.prenom.errors else ""), placeholder="Prénom") }}
                {{ form.prenom.label(class="form-label") }}
                {% if form.prenom.errors %}
                    <div class="invalid-feedback">
                        {% for error in form.prenom.errors %}
                            <small>{{ error }}</small>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-floating">
                {{ form.nom(class="form-control" + (" is-invalid" if form.nom.errors else ""), placeholder="Nom") }}
                {{ form.nom.label(class="form-label") }}
                {% if form.nom.errors %}
                    <div class="invalid-feedback">
                        {% for error in form.nom.errors %}
                            <small>{{ error }}</small>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="form-floating">
        {{ form.email(class="form-control" + (" is-invalid" if form.email.errors else ""), placeholder="Email") }}
        {{ form.email.label(class="form-label") }}
        {% if form.email.errors %}
            <div class="invalid-feedback">
                {% for error in form.email.errors %}
                    <small>{{ error }}</small>
                {% endfor %}
            </div>
        {% endif %}
    </div>
    
    <div class="form-floating">
        {{ form.username(class="form-control" + (" is-invalid" if form.username.errors else ""), placeholder="Nom d'utilisateur") }}
        {{ form.username.label(class="form-label") }}
        {% if form.username.errors %}
            <div class="invalid-feedback">
                {% for error in form.username.errors %}
                    <small>{{ error }}</small>
                {% endfor %}
            </div>
        {% endif %}
    </div>
    
    <div class="form-floating">
        {{ form.telephone(class="form-control", placeholder="Téléphone (optionnel)") }}
        {{ form.telephone.label(class="form-label") }}
    </div>
    
    {% if not role %}
    <div class="form-floating">
        {{ form.role(class="form-select" + (" is-invalid" if form.role.errors else "")) }}
        {{ form.role.label(class="form-label") }}
        {% if form.role.errors %}
            <div class="invalid-feedback">
                {% for error in form.role.errors %}
                    <small>{{ error }}</small>
                {% endfor %}
            </div>
        {% endif %}
    </div>
    {% else %}
    <div class="form-floating">
        <input type="text" class="form-control" value="{{ role.title() }}" readonly style="background-color: #f8f9fa;">
        <label class="form-label">Rôle sélectionné</label>
    </div>
    {% endif %}
    
    <div class="form-floating">
        {{ form.password(class="form-control" + (" is-invalid" if form.password.errors else ""), placeholder="Mot de passe") }}
        {{ form.password.label(class="form-label") }}
        <div class="password-strength" id="passwordStrength"></div>
        {% if form.password.errors %}
            <div class="invalid-feedback">
                {% for error in form.password.errors %}
                    <small>{{ error }}</small>
                {% endfor %}
            </div>
        {% endif %}
    </div>
    
    <div class="form-floating">
        {{ form.password_confirm(class="form-control" + (" is-invalid" if form.password_confirm.errors else ""), placeholder="Confirmer le mot de passe") }}
        {{ form.password_confirm.label(class="form-label") }}
        {% if form.password_confirm.errors %}
            <div class="invalid-feedback">
                {% for error in form.password_confirm.errors %}
                    <small>{{ error }}</small>
                {% endfor %}
            </div>
        {% endif %}
    </div>
    
    <div class="form-check">
        <input class="form-check-input" type="checkbox" id="terms" required>
        <label class="form-check-label" for="terms">
            J'accepte les <a href="#" class="auth-link">conditions d'utilisation</a> et la <a href="#" class="auth-link">politique de confidentialité</a>
        </label>
    </div>
    
    {{ form.submit(class="btn btn-primary btn-auth") }}
</form>

<div class="text-center mt-4">
    <p class="text-muted">
        Déjà un compte ? 
        <a href="{{ url_for('auth.login') }}" class="auth-link">Se connecter</a>
    </p>
</div>
{% endblock %}

{% block auth_scripts %}
<style>
.password-strength {
    margin-top: 0.5rem;
    font-size: 0.75rem;
}

.strength-bar {
    height: 4px;
    border-radius: 2px;
    margin-top: 0.25rem;
    transition: all 0.3s ease;
}

.strength-weak { background-color: #ef4444; width: 25%; }
.strength-fair { background-color: #f59e0b; width: 50%; }
.strength-good { background-color: #10b981; width: 75%; }
.strength-strong { background-color: #059669; width: 100%; }

.role-info {
    background-color: #f0f9ff;
    border: 1px solid #0ea5e9;
    border-radius: 8px;
    padding: 0.75rem;
    margin-top: 0.5rem;
    font-size: 0.875rem;
    color: #0369a1;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Animation d'entrée pour les champs
    const formFields = document.querySelectorAll('.form-floating, .form-check');
    formFields.forEach((field, index) => {
        field.style.opacity = '0';
        field.style.transform = 'translateY(20px)';
        setTimeout(() => {
            field.style.transition = 'all 0.5s ease';
            field.style.opacity = '1';
            field.style.transform = 'translateY(0)';
        }, index * 50);
    });
    
    // Validation du mot de passe en temps réel
    const passwordField = document.getElementById('password');
    const confirmPasswordField = document.getElementById('password_confirm');
    const strengthIndicator = document.getElementById('passwordStrength');
    
    if (passwordField) {
        passwordField.addEventListener('input', function() {
            checkPasswordStrength(this.value);
            validatePasswordMatch();
        });
    }
    
    if (confirmPasswordField) {
        confirmPasswordField.addEventListener('input', validatePasswordMatch);
    }
    
    function checkPasswordStrength(password) {
        let strength = 0;
        let feedback = '';
        
        if (password.length >= 8) strength++;
        if (/[a-z]/.test(password)) strength++;
        if (/[A-Z]/.test(password)) strength++;
        if (/[0-9]/.test(password)) strength++;
        if (/[^A-Za-z0-9]/.test(password)) strength++;
        
        const strengthBar = document.createElement('div');
        strengthBar.className = 'strength-bar';
        
        switch (strength) {
            case 0:
            case 1:
                strengthBar.className += ' strength-weak';
                feedback = 'Mot de passe faible';
                break;
            case 2:
                strengthBar.className += ' strength-fair';
                feedback = 'Mot de passe moyen';
                break;
            case 3:
            case 4:
                strengthBar.className += ' strength-good';
                feedback = 'Mot de passe bon';
                break;
            case 5:
                strengthBar.className += ' strength-strong';
                feedback = 'Mot de passe fort';
                break;
        }
        
        strengthIndicator.innerHTML = `
            <div style="color: ${strength <= 1 ? '#ef4444' : strength <= 2 ? '#f59e0b' : '#10b981'};">
                ${feedback}
            </div>
            ${strengthBar.outerHTML}
        `;
    }
    
    function validatePasswordMatch() {
        const password = passwordField.value;
        const confirmPassword = confirmPasswordField.value;
        
        confirmPasswordField.classList.remove('is-valid', 'is-invalid');
        
        if (confirmPassword && password !== confirmPassword) {
            confirmPasswordField.classList.add('is-invalid');
        } else if (confirmPassword && password === confirmPassword) {
            confirmPasswordField.classList.add('is-valid');
        }
    }
    
    // Information sur les rôles
    const roleField = document.getElementById('role');
    if (roleField) {
        roleField.addEventListener('change', function() {
            showRoleInfo(this.value);
        });
    }
    
    function showRoleInfo(role) {
        const existingInfo = document.querySelector('.role-info');
        if (existingInfo) {
            existingInfo.remove();
        }
        
        let infoText = '';
        switch (role) {
            case 'patient':
                infoText = 'En tant que patient, vous pourrez prendre rendez-vous et consulter votre dossier médical.';
                break;
            case 'medecin':
                infoText = 'En tant que médecin, vous aurez accès à la gestion des patients et des consultations.';
                break;
            case 'secretaire':
                infoText = 'En tant que secrétaire, vous pourrez gérer les rendez-vous et les dossiers patients.';
                break;
        }
        
        if (infoText) {
            const infoDiv = document.createElement('div');
            infoDiv.className = 'role-info';
            infoDiv.innerHTML = `<i class="fas fa-info-circle me-2"></i>${infoText}`;
            roleField.parentElement.appendChild(infoDiv);
        }
    }
    
    // Validation de l'email en temps réel
    const emailField = document.getElementById('email');
    if (emailField) {
        emailField.addEventListener('blur', function() {
            validateEmail(this);
        });
    }
    
    function validateEmail(field) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        const isValid = emailRegex.test(field.value);
        
        field.classList.remove('is-valid', 'is-invalid');
        if (field.value && isValid) {
            field.classList.add('is-valid');
        } else if (field.value && !isValid) {
            field.classList.add('is-invalid');
        }
    }
    
    // Animation du bouton de soumission
    const submitBtn = document.querySelector('.btn-auth');
    if (submitBtn) {
        submitBtn.addEventListener('click', function(e) {
            const form = this.form;
            const termsCheckbox = document.getElementById('terms');
            
            if (!termsCheckbox.checked) {
                e.preventDefault();
                alert('Vous devez accepter les conditions d\'utilisation pour continuer.');
                return;
            }
            
            if (form.checkValidity()) {
                this.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Création du compte...';
                this.disabled = true;
            }
        });
    }
});
</script>
{% endblock %}
