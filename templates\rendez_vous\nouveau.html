{% extends "base.html" %}

{% block title %}Nouveau rendez-vous - Ilaji Medical{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="display-6 text-primary mb-1">
                    <i class="fas fa-calendar-plus me-3"></i>Nouveau rendez-vous
                </h1>
                <p class="text-muted mb-0">Planifier un nouveau rendez-vous</p>
            </div>
            <div>
                <a href="{{ url_for('rendez_vous') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Retour à la liste
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8 mx-auto">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-gradient-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-plus me-2"></i>Informations du rendez-vous
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="patient_id" class="form-label">Patient *</label>
                            <select class="form-select" id="patient_id" name="patient_id" required>
                                <option value="">Choisir un patient</option>
                                {% for patient in patients %}
                                    <option value="{{ patient.id }}">{{ patient.nom_complet }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="medecin_id" class="form-label">Médecin *</label>
                            <select class="form-select" id="medecin_id" name="medecin_id" required>
                                <option value="">Choisir un médecin</option>
                                {% for medecin in medecins %}
                                    <option value="{{ medecin.id }}">{{ medecin.nom_complet }} - {{ medecin.specialite }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="date_rdv" class="form-label">Date *</label>
                            <input type="date" class="form-control" id="date_rdv" name="date_rdv" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="heure_rdv" class="form-label">Heure *</label>
                            <input type="time" class="form-control" id="heure_rdv" name="heure_rdv" required>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="duree" class="form-label">Durée (minutes)</label>
                            <select class="form-select" id="duree" name="duree">
                                <option value="15">15 minutes</option>
                                <option value="30" selected>30 minutes</option>
                                <option value="45">45 minutes</option>
                                <option value="60">1 heure</option>
                                <option value="90">1h30</option>
                                <option value="120">2 heures</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="priorite" class="form-label">Priorité</label>
                            <select class="form-select" id="priorite" name="priorite">
                                <option value="normale" selected>Normale</option>
                                <option value="urgente">Urgente</option>
                                <option value="tres_urgente">Très urgente</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="motif" class="form-label">Motif de consultation</label>
                        <textarea class="form-control" id="motif" name="motif" rows="3" placeholder="Décrivez le motif de la consultation..."></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="type_prise_rdv" class="form-label">Type de prise de RDV</label>
                            <select class="form-select" id="type_prise_rdv" name="type_prise_rdv">
                                <option value="en_ligne" selected>En ligne</option>
                                <option value="telephone">Téléphone</option>
                                <option value="papier">Papier</option>
                                <option value="urgence">Urgence</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="numero_telephone_contact" class="form-label">Téléphone de contact</label>
                            <input type="tel" class="form-control" id="numero_telephone_contact" name="numero_telephone_contact">
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="notes" class="form-label">Notes</label>
                        <textarea class="form-control" id="notes" name="notes" rows="2" placeholder="Notes supplémentaires..."></textarea>
                    </div>
                    
                    <div class="d-flex justify-content-end gap-2">
                        <a href="{{ url_for('rendez_vous') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>Annuler
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Planifier le RDV
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Définir la date minimale à aujourd'hui
document.getElementById('date_rdv').min = new Date().toISOString().split('T')[0];
</script>
{% endblock %}
