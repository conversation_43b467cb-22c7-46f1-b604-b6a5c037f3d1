{% extends "base.html" %}

{% block title %}Liste des médecins - Ilaji Medical{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="display-6 text-primary mb-1">
                    <i class="fas fa-user-md me-3"></i>Liste des médecins
                </h1>
                <p class="text-muted mb-0">Gestion des médecins du cabinet</p>
            </div>
            <div>
                <a href="{{ url_for('nouveau_medecin') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>Nouveau médecin
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-gradient-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>Médecins actifs ({{ medecins|length }})
                </h5>
            </div>
            <div class="card-body">
                {% if medecins %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Nom complet</th>
                                    <th>Spécialité</th>
                                    <th>Téléphone</th>
                                    <th>Email</th>
                                    <th>Tarif consultation</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for medecin in medecins %}
                                <tr>
                                    <td>
                                        <strong>{{ medecin.nom_complet }}</strong>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ medecin.specialite }}</span>
                                    </td>
                                    <td>{{ medecin.telephone or '-' }}</td>
                                    <td>{{ medecin.email or '-' }}</td>
                                    <td>
                                        {% if medecin.tarif_consultation %}
                                            {{ medecin.tarif_consultation }}€
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary" title="Voir détails">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-outline-warning" title="Modifier">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-outline-danger" title="Supprimer">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-user-md fa-4x text-muted mb-3"></i>
                        <h4 class="text-muted">Aucun médecin enregistré</h4>
                        <p class="text-muted">Commencez par ajouter votre premier médecin</p>
                        <a href="{{ url_for('nouveau_medecin') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Ajouter un médecin
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
