# 🔐 Identifiants de Test - Ilaji Medical

## 📋 Comptes de Connexion

### 👨‍💼 **Administrateur**
- **Username** : `mehdi<PERSON>aoui`
- **<PERSON>t de passe** : `mehdi123`
- **R<PERSON>le** : Administration complète du système
- **Dashboard** : `/admin/dashboard`

### 👨‍⚕️ **M<PERSON><PERSON>cins**
- **Dr. <PERSON>**
  - Username : `dr.martin`
  - <PERSON>t de passe : `medecin123`
- **Dr. <PERSON>**
  - Username : `dr.durand`
  - <PERSON><PERSON> de passe : `medecin456`
- **Dashboard** : `/medecin/dashboard`

### 👩‍💼 **Se<PERSON><PERSON>taires**
- **<PERSON> (<PERSON><PERSON><PERSON><PERSON>)**
  - Username : `marie.sec`
  - Mot de passe : `secret123`
- **Julie (Admin)**
  - Username : `julie.admin`
  - Mot de passe : `admin456`
- **Dashboard** : `/secretaire/dashboard`

### 🏥 **Patients (Accès par CIN)**
- **CIN valides** :
  - `12345678`
  - `87654321`
  - `11223344`
  - `99887766`
- **Dashboard** : `/patient/dashboard`

## 🚀 Comment tester

1. **Accédez à** : `http://127.0.0.1:5000`
2. **Sélectionnez** votre rôle (Admin, Médecin, Secrétaire, Patient)
3. **Choisissez** "J'ai déjà un compte"
4. **Connectez-vous** avec les identifiants ci-dessus

## 🔒 Sécurité

- ✅ Validation des identifiants
- ✅ Messages d'erreur appropriés
- ✅ Redirection selon le rôle
- ✅ Interface sécurisée pour chaque type d'utilisateur

## 📝 Notes

- Les identifiants sont actuellement stockés en dur dans le code
- Pour la production, ils seront remplacés par une base de données sécurisée
- Les mots de passe seront hashés avec bcrypt
- Système de sessions et tokens d'authentification à implémenter
