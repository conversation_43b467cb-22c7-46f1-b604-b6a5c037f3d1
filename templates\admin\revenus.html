{% extends "base.html" %}

{% block title %}Détails des Revenus - Ilaji Medical{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="display-6 text-warning mb-1">
                    <i class="fas fa-euro-sign me-3"></i>Détails des Revenus
                </h1>
                <p class="text-muted mb-0">Analyse financière et suivi des revenus</p>
            </div>
            <div>
                <a href="/admin/dashboard" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Retour Dashboard
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Statistiques financières -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <i class="fas fa-euro-sign fa-2x text-success mb-2"></i>
                <h4 class="text-success">{{ revenus.total_mois }}€</h4>
                <p class="text-muted mb-0">Revenus ce mois</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <i class="fas fa-calendar-check fa-2x text-info mb-2"></i>
                <h4 class="text-info">{{ revenus.total_consultations }}</h4>
                <p class="text-muted mb-0">Consultations</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <i class="fas fa-calculator fa-2x text-warning mb-2"></i>
                <h4 class="text-warning">{{ "%.2f"|format(revenus.total_mois / revenus.total_consultations if revenus.total_consultations > 0 else 0) }}€</h4>
                <p class="text-muted mb-0">Tarif moyen</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <i class="fas fa-chart-line fa-2x text-primary mb-2"></i>
                <h4 class="text-primary">+15%</h4>
                <p class="text-muted mb-0">Évolution</p>
            </div>
        </div>
    </div>
</div>

<!-- Graphique des revenus -->
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-area me-2"></i>Évolution des revenus
                </h5>
            </div>
            <div class="card-body">
                <canvas id="revenusChart" height="100"></canvas>
            </div>
        </div>
    </div>
    <div class="col-lg-4">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-pie-chart me-2"></i>Répartition par spécialité
                </h5>
            </div>
            <div class="card-body">
                <canvas id="specialitesChart" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Revenus par médecin -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user-md me-2"></i>Revenus par médecin
                </h5>
            </div>
            <div class="card-body">
                {% if revenus.revenus_par_medecin %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>Médecin</th>
                                <th>Spécialité</th>
                                <th>Consultations</th>
                                <th>Revenus</th>
                                <th>Tarif moyen</th>
                                <th>Performance</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for medecin in revenus.revenus_par_medecin %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="bg-success bg-gradient rounded-circle p-2 me-3">
                                            <i class="fas fa-user-md text-white"></i>
                                        </div>
                                        <strong>{{ medecin.nom }}</strong>
                                    </div>
                                </td>
                                <td><span class="badge bg-success">{{ medecin.specialite }}</span></td>
                                <td>{{ medecin.consultations }}</td>
                                <td><strong>{{ medecin.revenus }}€</strong></td>
                                <td>{{ medecin.tarif_moyen }}€</td>
                                <td>
                                    <div class="progress" style="height: 20px;">
                                        <div class="progress-bar bg-success" style="width: {{ medecin.performance }}%">
                                            {{ medecin.performance }}%
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-chart-bar fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">Aucune donnée de revenus</h5>
                    <p class="text-muted">Les revenus apparaîtront ici une fois que des consultations seront enregistrées.</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Actions rapides -->
<div class="row">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-gradient-warning text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-tools me-2"></i>Actions rapides
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="/admin/export-revenus" class="btn btn-outline-success w-100 py-3">
                            <i class="fas fa-file-excel fa-2x mb-2 d-block"></i>
                            Exporter Excel
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="/admin/rapport-mensuel" class="btn btn-outline-info w-100 py-3">
                            <i class="fas fa-file-pdf fa-2x mb-2 d-block"></i>
                            Rapport mensuel
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="/admin/facturation" class="btn btn-outline-warning w-100 py-3">
                            <i class="fas fa-file-invoice fa-2x mb-2 d-block"></i>
                            Facturation
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="/admin/parametres-tarifs" class="btn btn-outline-primary w-100 py-3">
                            <i class="fas fa-cog fa-2x mb-2 d-block"></i>
                            Paramètres tarifs
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Graphique évolution des revenus
const revenusCtx = document.getElementById('revenusChart').getContext('2d');
new Chart(revenusCtx, {
    type: 'line',
    data: {
        labels: ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Jun'],
        datasets: [{
            label: 'Revenus (€)',
            data: [1200, 1800, 1500, 2200, 1900, {{ revenus.total_mois }}],
            borderColor: '#28a745',
            backgroundColor: 'rgba(40, 167, 69, 0.1)',
            tension: 0.4,
            fill: true
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return value + '€';
                    }
                }
            }
        }
    }
});

// Graphique répartition par spécialité
const specialitesCtx = document.getElementById('specialitesChart').getContext('2d');
new Chart(specialitesCtx, {
    type: 'doughnut',
    data: {
        labels: ['Médecine générale', 'Cardiologie', 'Dermatologie', 'Autres'],
        datasets: [{
            data: [40, 25, 20, 15],
            backgroundColor: [
                '#007bff',
                '#28a745',
                '#ffc107',
                '#6c757d'
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});
</script>
{% endblock %}
