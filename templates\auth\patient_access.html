<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Accès <PERSON> - <PERSON><PERSON><PERSON></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --info-color: #06b6d4;
            --info-dark: #0891b2;
            --primary-color: #2563eb;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, var(--info-color) 0%, var(--info-dark) 100%);
            min-height: 100vh;
            margin: 0;
            padding: 0;
        }
        
        .access-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem 1rem;
        }
        
        .access-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
            max-width: 500px;
            width: 100%;
        }
        
        .access-header {
            background: linear-gradient(135deg, var(--info-color) 0%, var(--info-dark) 100%);
            color: white;
            padding: 2.5rem 2rem;
            text-align: center;
        }
        
        .patient-icon {
            font-size: 3.5rem;
            margin-bottom: 1rem;
            display: block;
        }
        
        .access-header h1 {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            color: white;
        }
        
        .access-header p {
            font-size: 1rem;
            opacity: 0.9;
            margin: 0;
        }
        
        .access-body {
            padding: 2.5rem 2rem;
        }
        
        .cin-section {
            background: #f8fafc;
            border: 2px solid #e2e8f0;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .cin-section h3 {
            color: var(--info-color);
            margin-bottom: 1rem;
            font-weight: 600;
        }
        
        .form-floating {
            margin-bottom: 1.5rem;
        }
        
        .form-control {
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            padding: 0.75rem 1rem;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: var(--info-color);
            box-shadow: 0 0 0 0.2rem rgba(6, 182, 212, 0.25);
        }
        
        .btn-access {
            background: linear-gradient(135deg, var(--info-color) 0%, var(--info-dark) 100%);
            border: none;
            border-radius: 10px;
            color: white;
            padding: 0.75rem 2rem;
            font-weight: 600;
            font-size: 1rem;
            width: 100%;
            transition: all 0.3s ease;
            margin-bottom: 1rem;
        }
        
        .btn-access:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px -5px rgba(6, 182, 212, 0.4);
            color: white;
        }
        
        .btn-create {
            background: linear-gradient(135deg, var(--primary-color) 0%, #1d4ed8 100%);
            border: none;
            border-radius: 10px;
            color: white;
            padding: 0.75rem 2rem;
            font-weight: 600;
            font-size: 1rem;
            width: 100%;
            transition: all 0.3s ease;
        }
        
        .btn-create:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px -5px rgba(37, 99, 235, 0.4);
            color: white;
        }
        
        .divider {
            text-align: center;
            margin: 2rem 0;
            position: relative;
        }
        
        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: #e2e8f0;
        }
        
        .divider span {
            background: white;
            padding: 0 1rem;
            color: #6b7280;
            font-weight: 500;
        }
        
        .create-section {
            background: #fefefe;
            border: 2px solid #e2e8f0;
            border-radius: 15px;
            padding: 2rem;
            text-align: center;
        }
        
        .create-section h3 {
            color: var(--primary-color);
            margin-bottom: 1rem;
            font-weight: 600;
        }
        
        .create-section p {
            color: #6b7280;
            margin-bottom: 1.5rem;
            line-height: 1.6;
        }
        
        .back-link {
            text-align: center;
            margin-top: 2rem;
        }
        
        .back-link a {
            color: #6b7280;
            text-decoration: none;
            font-size: 0.9rem;
            transition: color 0.3s ease;
        }
        
        .back-link a:hover {
            color: #374151;
        }
        
        .alert {
            border-radius: 10px;
            border: none;
            margin-bottom: 1.5rem;
        }
        
        .cin-info {
            background: #eff6ff;
            border: 1px solid #bfdbfe;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1.5rem;
            font-size: 0.9rem;
            color: #1e40af;
        }
        
        @media (max-width: 768px) {
            .access-header h1 {
                font-size: 1.5rem;
            }
            
            .access-header,
            .access-body {
                padding: 2rem 1.5rem;
            }
            
            .cin-section,
            .create-section {
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="access-container">
        <div class="access-card">
            <div class="access-header">
                <i class="fas fa-user patient-icon"></i>
                <h1>Accès Patient</h1>
                <p>Votre dossier médical personnel</p>
            </div>
            
            <div class="access-body">
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ 'danger' if category == 'error' else 'success' }}">
                                <i class="fas fa-{{ 'exclamation-triangle' if category == 'error' else 'check-circle' }} me-2"></i>
                                {{ message }}
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}
                
                <!-- Section CIN pour patients existants -->
                <div class="cin-section">
                    <h3><i class="fas fa-id-card me-2"></i>J'ai déjà un dossier</h3>
                    <p class="text-muted mb-3">Saisissez votre numéro CIN pour accéder à votre dossier médical</p>
                    
                    <div class="cin-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Format CIN :</strong> 8 chiffres (ex: 12345678)
                    </div>
                    
                    <form method="POST" action="/auth/patient-access">
                        <input type="hidden" name="action" value="login">
                        
                        <div class="form-floating">
                            <input type="text" class="form-control" id="cin" name="cin" placeholder="Numéro CIN" 
                                   pattern="[0-9]{8}" maxlength="8" required>
                            <label for="cin">
                                <i class="fas fa-id-card me-2"></i>Numéro CIN
                            </label>
                        </div>
                        
                        <button type="submit" class="btn btn-access">
                            <i class="fas fa-sign-in-alt me-2"></i>Accéder à mon dossier
                        </button>
                    </form>
                </div>
                
                <!-- Séparateur -->
                <div class="divider">
                    <span>OU</span>
                </div>
                
                <!-- Section création de compte -->
                <div class="create-section">
                    <h3><i class="fas fa-user-plus me-2"></i>Première visite</h3>
                    <p>
                        Vous n'avez pas encore de dossier médical ? 
                        Créez votre compte patient pour commencer votre suivi médical.
                    </p>
                    
                    <form method="POST" action="/auth/patient-access">
                        <input type="hidden" name="action" value="register">
                        
                        <button type="submit" class="btn btn-create">
                            <i class="fas fa-user-plus me-2"></i>Créer mon dossier patient
                        </button>
                    </form>
                </div>
                
                <div class="back-link">
                    <a href="/auth/role-choice?role=patient">
                        <i class="fas fa-arrow-left me-1"></i>Retour aux options
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Animation d'entrée
        document.addEventListener('DOMContentLoaded', function() {
            const card = document.querySelector('.access-card');
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';
            
            setTimeout(() => {
                card.style.transition = 'all 0.6s ease';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, 100);
        });
        
        // Validation du CIN en temps réel
        document.getElementById('cin').addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, ''); // Supprimer tout sauf les chiffres
            if (value.length > 8) {
                value = value.substring(0, 8);
            }
            e.target.value = value;
        });
    </script>
</body>
</html>
