﻿/* <PERSON><PERSON><PERSON> - Styles CSS Avancés */

/* Variables CSS */
:root {
    --primary-color: #2563eb;
    --primary-gradient: linear-gradient(135deg, #2563eb, #1d4ed8);
    --secondary-color: #64748b;
    --success-color: #059669;
    --info-color: #0891b2;
    --warning-color: #d97706;
    --danger-color: #dc2626;
    --light-color: #f8fafc;
    --dark-color: #1e293b;
    --border-radius: 12px;
    --box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Corps de la page */
body {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON>', <PERSON><PERSON>, sans-serif;
    line-height: 1.6;
}

/* Navigation améliorée */
.navbar {
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.bg-gradient-primary {
    background: var(--primary-gradient) !important;
}

.navbar-brand {
    font-size: 1.5rem;
    font-weight: 700;
    letter-spacing: -0.025em;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: var(--transition);
    border-radius: 8px;
    margin: 0 4px;
    padding: 8px 16px !important;
}

.navbar-nav .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
}

.navbar-nav .nav-link.active {
    background-color: rgba(255, 255, 255, 0.2);
    font-weight: 600;
}

/* Cartes améliorées */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    overflow: hidden;
}

.card:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.card-hover {
    cursor: pointer;
}

.card-header {
    background: transparent;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    padding: 1.5rem;
    font-weight: 600;
}

.card-body {
    padding: 1.5rem;
}

/* Boutons modernisés */
.btn {
    border-radius: 10px;
    font-weight: 500;
    padding: 12px 24px;
    transition: var(--transition);
    border: none;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.btn-primary {
    background: var(--primary-gradient);
    color: white;
}

.btn-outline-primary {
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
}

.btn-outline-primary:hover {
    background: var(--primary-gradient);
    border-color: transparent;
    color: white;
}

/* Badges modernisés */
.badge {
    border-radius: 8px;
    font-weight: 500;
    padding: 6px 12px;
    font-size: 0.75rem;
    letter-spacing: 0.025em;
}

/* Tables améliorées */
.table {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

.table thead th {
    background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
    border: none;
    font-weight: 600;
    color: var(--dark-color);
    padding: 1rem;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.05em;
}

.table tbody tr {
    transition: var(--transition);
    border: none;
}

.table tbody tr:hover {
    background-color: rgba(37, 99, 235, 0.05);
    transform: scale(1.01);
}

.table tbody td {
    padding: 1rem;
    border-color: rgba(0, 0, 0, 0.05);
    vertical-align: middle;
}

/* Formulaires améliorés */
.form-control, .form-select {
    border-radius: 10px;
    border: 2px solid #e2e8f0;
    padding: 12px 16px;
    transition: var(--transition);
    background-color: #fff;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    transform: translateY(-1px);
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 8px;
    font-size: 0.875rem;
}

/* Alertes améliorées */
.alert {
    border: none;
    border-radius: var(--border-radius);
    padding: 1rem 1.5rem;
    font-weight: 500;
    box-shadow: var(--box-shadow);
}

.alert-success {
    background: linear-gradient(135deg, #d1fae5, #a7f3d0);
    color: #065f46;
}

.alert-danger {
    background: linear-gradient(135deg, #fee2e2, #fecaca);
    color: #991b1b;
}

/* Statistiques du tableau de bord */
.display-6 {
    font-weight: 700;
    letter-spacing: -0.025em;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.pulse-animation {
    animation: pulse 2s infinite;
}

/* Effets de survol pour les icônes */
.fa:hover {
    transform: scale(1.1);
    transition: var(--transition);
}

/* Dropdown amélioré */
.dropdown-menu {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    padding: 8px;
    margin-top: 8px;
}

.dropdown-item {
    border-radius: 8px;
    padding: 12px 16px;
    transition: var(--transition);
    font-weight: 500;
}

.dropdown-item:hover {
    background-color: rgba(37, 99, 235, 0.1);
    color: var(--primary-color);
    transform: translateX(4px);
}

/* Footer amélioré */
footer {
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
    border-top: 1px solid rgba(0, 0, 0, 0.05);
}

/* Responsive amélioré */
@media (max-width: 768px) {
    .card {
        margin-bottom: 1rem;
    }
    
    .btn {
        padding: 10px 20px;
        font-size: 0.875rem;
    }
    
    .display-6 {
        font-size: 1.75rem;
    }
}

/* Scrollbar personnalisée */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-gradient);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #1d4ed8, #1e40af);
}

/* États de chargement */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Effets de focus améliorés */
.btn:focus,
.form-control:focus,
.form-select:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Styles pour les priorités */
.priority-normale { border-left: 4px solid #6b7280; }
.priority-urgente { border-left: 4px solid #f59e0b; }
.priority-tres-urgente { border-left: 4px solid #ef4444; }

/* Styles pour les statuts */
.status-planifie { background-color: #dbeafe; color: #1e40af; }
.status-confirme { background-color: #d1fae5; color: #065f46; }
.status-annule { background-color: #fee2e2; color: #991b1b; }
.status-termine { background-color: #f3f4f6; color: #374151; }
.status-en-cours { background-color: #fef3c7; color: #92400e; }

/* Amélioration de l'accessibilité */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Mode sombre (optionnel) */
@media (prefers-color-scheme: dark) {
    :root {
        --light-color: #1e293b;
        --dark-color: #f8fafc;
    }
    
    body {
        background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
        color: #f8fafc;
    }
    
    .card {
        background-color: #334155;
        color: #f8fafc;
    }
    
    .table {
        background-color: #334155;
        color: #f8fafc;
    }
}
