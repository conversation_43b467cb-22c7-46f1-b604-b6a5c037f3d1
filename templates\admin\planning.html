{% extends "base.html" %}

{% block title %}Planning Hebdomadaire - Ilaji Medical{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="display-6 text-info mb-1">
                    <i class="fas fa-calendar-week me-3"></i>Planning Hebdomadaire
                </h1>
                <p class="text-muted mb-0">Vue d'ensemble des rendez-vous de la semaine</p>
            </div>
            <div>
                <a href="/nouveau-rendez-vous" class="btn btn-info">
                    <i class="fas fa-plus me-2"></i>Nouveau RDV
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Navigation semaine -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <button class="btn btn-outline-secondary">
                        <i class="fas fa-chevron-left me-2"></i>Semaine précédente
                    </button>
                    <h5 class="mb-0">
                        Semaine du {{ planning_semaine[0].date }} au {{ planning_semaine[6].date }}
                    </h5>
                    <button class="btn btn-outline-secondary">
                        Semaine suivante<i class="fas fa-chevron-right ms-2"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Planning de la semaine -->
<div class="row">
    {% for jour in planning_semaine %}
    <div class="col-lg-12 mb-4">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-gradient-info text-white">
                <div class="d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">
                        <i class="fas fa-calendar-day me-2"></i>
                        {{ jour.jour_fr }} {{ jour.date }}
                    </h6>
                    <button class="btn btn-light btn-sm" onclick="ajouterRdv('{{ jour.date }}')">
                        <i class="fas fa-plus me-1"></i>Ajouter RDV
                    </button>
                </div>
            </div>
            <div class="card-body">
                {% if jour.rdv %}
                    <!-- Affichage des RDV existants -->
                    <div class="row">
                        {% for rdv in jour.rdv %}
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="card border-start border-4 border-primary">
                                <div class="card-body py-2">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h6 class="card-title mb-1">{{ rdv.heure }}</h6>
                                            <p class="card-text mb-1">
                                                <strong>{{ rdv.patient_nom }}</strong><br>
                                                <small class="text-muted">Dr. {{ rdv.medecin_nom }}</small>
                                            </p>
                                            <span class="badge bg-primary">{{ rdv.motif }}</span>
                                        </div>
                                        <div class="dropdown">
                                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                                                <i class="fas fa-ellipsis-v"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li><a class="dropdown-item" href="#"><i class="fas fa-eye me-2"></i>Détails</a></li>
                                                <li><a class="dropdown-item" href="#"><i class="fas fa-edit me-2"></i>Modifier</a></li>
                                                <li><hr class="dropdown-divider"></li>
                                                <li><a class="dropdown-item text-danger" href="#"><i class="fas fa-trash me-2"></i>Annuler</a></li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <!-- Aucun RDV pour ce jour -->
                    <div class="text-center py-4">
                        <i class="fas fa-calendar-times fa-2x text-muted mb-3"></i>
                        <p class="text-muted mb-3">Aucun rendez-vous programmé pour ce jour</p>
                        <button class="btn btn-outline-info btn-sm" onclick="ajouterRdv('{{ jour.date }}')">
                            <i class="fas fa-plus me-2"></i>Programmer un RDV
                        </button>
                    </div>
                {% endif %}
                
                <!-- Créneaux horaires disponibles -->
                <div class="mt-3">
                    <h6 class="text-muted mb-2">Créneaux disponibles:</h6>
                    <div class="d-flex flex-wrap gap-2">
                        {% for heure in ['08:00', '08:30', '09:00', '09:30', '10:00', '10:30', '11:00', '11:30', '14:00', '14:30', '15:00', '15:30', '16:00', '16:30', '17:00', '17:30'] %}
                        <button class="btn btn-outline-success btn-sm" onclick="ajouterRdvHeure('{{ jour.date }}', '{{ heure }}')">
                            {{ heure }}
                        </button>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
</div>

<!-- Statistiques de la semaine -->
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card border-0 shadow-sm text-center">
            <div class="card-body">
                <i class="fas fa-calendar-check fa-2x text-success mb-2"></i>
                <h4 class="text-success">0</h4>
                <p class="text-muted mb-0">RDV cette semaine</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 shadow-sm text-center">
            <div class="card-body">
                <i class="fas fa-user-md fa-2x text-info mb-2"></i>
                <h4 class="text-info">{{ medecins|length if medecins is defined else 0 }}</h4>
                <p class="text-muted mb-0">Médecins actifs</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 shadow-sm text-center">
            <div class="card-body">
                <i class="fas fa-clock fa-2x text-warning mb-2"></i>
                <h4 class="text-warning">112</h4>
                <p class="text-muted mb-0">Créneaux libres</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 shadow-sm text-center">
            <div class="card-body">
                <i class="fas fa-euro-sign fa-2x text-primary mb-2"></i>
                <h4 class="text-primary">0€</h4>
                <p class="text-muted mb-0">Revenus prévus</p>
            </div>
        </div>
    </div>
</div>

<script>
function ajouterRdv(date) {
    window.location.href = `/nouveau-rendez-vous?date=${date}`;
}

function ajouterRdvHeure(date, heure) {
    window.location.href = `/nouveau-rendez-vous?date=${date}&heure=${heure}`;
}
</script>
{% endblock %}
