{% extends "base.html" %}

{% block title %}Détails Administrateur - Ilaji Medical{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="display-6 text-danger mb-1">
                    <i class="fas fa-user-shield me-3"></i>Détails Administrateur
                </h1>
                <p class="text-muted mb-0">Informations complètes de {{ utilisateur.prenom }} {{ utilisateur.nom }}</p>
            </div>
            <div>
                <a href="/admin/utilisateurs" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Retour à la liste
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Informations principales -->
    <div class="col-lg-8">
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-gradient-danger text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user-shield me-2"></i>Informations personnelles
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted">Prénom</label>
                            <p class="fw-bold">{{ utilisateur.prenom }}</p>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label text-muted">Nom</label>
                            <p class="fw-bold">{{ utilisateur.nom }}</p>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label text-muted">Email</label>
                            <p class="fw-bold">
                                <i class="fas fa-envelope me-2 text-primary"></i>
                                <a href="mailto:{{ utilisateur.email }}" class="text-decoration-none">{{ utilisateur.email }}</a>
                            </p>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted">Téléphone</label>
                            <p class="fw-bold">
                                <i class="fas fa-phone me-2 text-success"></i>
                                <a href="tel:{{ utilisateur.telephone }}" class="text-decoration-none">{{ utilisateur.telephone }}</a>
                            </p>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label text-muted">Nom d'utilisateur</label>
                            <p class="fw-bold">
                                <i class="fas fa-user me-2 text-info"></i>
                                {{ utilisateur.username }}
                            </p>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label text-muted">Rôle</label>
                            <p>
                                <span class="badge bg-danger fs-6">
                                    <i class="fas fa-shield-alt me-1"></i>{{ utilisateur.role }}
                                </span>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Permissions et accès -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-key me-2"></i>Permissions et accès
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-success mb-3">
                            <i class="fas fa-check-circle me-2"></i>Accès autorisés
                        </h6>
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <i class="fas fa-users text-primary me-2"></i>
                                Gestion des utilisateurs
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-user-md text-success me-2"></i>
                                Gestion des médecins
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-user text-info me-2"></i>
                                Gestion des patients
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-calendar text-warning me-2"></i>
                                Planning et rendez-vous
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-euro-sign text-success me-2"></i>
                                Revenus et facturation
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-cog text-secondary me-2"></i>
                                Paramètres système
                            </li>
                        </ul>
                    </div>
                    
                    <div class="col-md-6">
                        <h6 class="text-info mb-3">
                            <i class="fas fa-shield-alt me-2"></i>Niveau de sécurité
                        </h6>
                        <div class="mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-1">
                                <span class="small">Accès administrateur</span>
                                <span class="small text-success fw-bold">100%</span>
                            </div>
                            <div class="progress" style="height: 8px;">
                                <div class="progress-bar bg-danger" style="width: 100%"></div>
                            </div>
                        </div>
                        
                        <div class="alert alert-warning" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>Accès complet :</strong> Cet utilisateur a tous les droits administrateur.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Actions rapides -->
    <div class="col-lg-4">
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-tools me-2"></i>Actions rapides
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="/admin/utilisateur/{{ utilisateur.id }}/modifier" class="btn btn-warning">
                        <i class="fas fa-edit me-2"></i>Modifier les informations
                    </a>
                    
                    <a href="/admin/reset-password/{{ utilisateur.id }}" class="btn btn-info">
                        <i class="fas fa-key me-2"></i>Réinitialiser mot de passe
                    </a>

                    <a href="/admin/historique-connexions/{{ utilisateur.id }}" class="btn btn-secondary">
                        <i class="fas fa-history me-2"></i>Historique des connexions
                    </a>
                    
                    <hr>
                    
                    <button class="btn btn-outline-danger" onclick="confirmerSuppression({{ utilisateur.id }}, '{{ utilisateur.prenom }} {{ utilisateur.nom }}')">
                        <i class="fas fa-trash me-2"></i>Supprimer le compte
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Statistiques -->
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>Statistiques
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6 mb-3">
                        <div class="border-end">
                            <h4 class="text-primary mb-1">0</h4>
                            <small class="text-muted">Connexions aujourd'hui</small>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <h4 class="text-success mb-1">0</h4>
                        <small class="text-muted">Actions effectuées</small>
                    </div>
                    <div class="col-12">
                        <small class="text-muted">
                            <i class="fas fa-clock me-1"></i>
                            Dernière connexion : Aujourd'hui
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function confirmerSuppression(id, nom) {
    if (confirm(`Êtes-vous sûr de vouloir supprimer l'administrateur "${nom}" ?\n\nCette action est irréversible et peut affecter l'accès au système.`)) {
        window.location.href = `/admin/utilisateur/${id}/supprimer`;
    }
}
</script>
{% endblock %}
