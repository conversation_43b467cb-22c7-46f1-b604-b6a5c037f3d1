<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bienvenue - <PERSON><PERSON><PERSON> Medical</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #2563eb;
            --primary-dark: #1d4ed8;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --info-color: #06b6d4;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 0;
        }
        
        .welcome-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem 1rem;
        }
        
        .welcome-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
            max-width: 900px;
            width: 100%;
        }
        
        .welcome-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: white;
            padding: 3rem 2rem;
            text-align: center;
        }
        
        .welcome-header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            color: white;
        }
        
        .welcome-header p {
            font-size: 1.1rem;
            opacity: 0.9;
            margin: 0;
        }
        
        .welcome-body {
            padding: 3rem 2rem;
        }
        
        .role-card {
            background: white;
            border: 2px solid #e2e8f0;
            border-radius: 15px;
            padding: 2rem;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            height: 100%;
            position: relative;
            overflow: hidden;
        }
        
        .role-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px -5px rgba(0, 0, 0, 0.15);
            border-color: var(--primary-color);
        }
        
        .role-card.admin {
            border-color: var(--danger-color);
        }
        
        .role-card.admin:hover {
            border-color: var(--danger-color);
            box-shadow: 0 15px 35px -5px rgba(239, 68, 68, 0.3);
        }
        
        .role-card.medecin {
            border-color: var(--success-color);
        }
        
        .role-card.medecin:hover {
            border-color: var(--success-color);
            box-shadow: 0 15px 35px -5px rgba(16, 185, 129, 0.3);
        }
        
        .role-card.secretaire {
            border-color: var(--warning-color);
        }
        
        .role-card.secretaire:hover {
            border-color: var(--warning-color);
            box-shadow: 0 15px 35px -5px rgba(245, 158, 11, 0.3);
        }
        
        .role-card.patient {
            border-color: var(--info-color);
        }
        
        .role-card.patient:hover {
            border-color: var(--info-color);
            box-shadow: 0 15px 35px -5px rgba(6, 182, 212, 0.3);
        }
        
        .role-icon {
            font-size: 3rem;
            margin-bottom: 1.5rem;
            display: block;
        }
        
        .role-icon.admin { color: var(--danger-color); }
        .role-icon.medecin { color: var(--success-color); }
        .role-icon.secretaire { color: var(--warning-color); }
        .role-icon.patient { color: var(--info-color); }
        
        .role-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #1f2937;
        }
        
        .role-description {
            color: #6b7280;
            font-size: 0.95rem;
            line-height: 1.6;
            margin-bottom: 1.5rem;
        }
        
        .role-features {
            list-style: none;
            padding: 0;
            margin: 0;
            text-align: left;
        }
        
        .role-features li {
            padding: 0.5rem 0;
            color: #4b5563;
            font-size: 0.9rem;
        }
        
        .role-features li i {
            color: var(--success-color);
            margin-right: 0.5rem;
            width: 16px;
        }
        
        .floating-shapes {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: -1;
        }
        
        .shape {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }
        
        .shape:nth-child(1) {
            width: 80px;
            height: 80px;
            top: 10%;
            left: 10%;
            animation-delay: 0s;
        }
        
        .shape:nth-child(2) {
            width: 120px;
            height: 120px;
            top: 70%;
            right: 10%;
            animation-delay: 2s;
        }
        
        .shape:nth-child(3) {
            width: 60px;
            height: 60px;
            top: 40%;
            left: 80%;
            animation-delay: 4s;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
        
        .welcome-footer {
            background-color: #f8fafc;
            padding: 2rem;
            text-align: center;
            border-top: 1px solid #e2e8f0;
        }
        
        .btn-role {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            border: none;
            border-radius: 10px;
            color: white;
            padding: 0.75rem 2rem;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            margin-top: 1rem;
        }
        
        .btn-role:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px -5px rgba(37, 99, 235, 0.4);
            color: white;
        }
        
        @media (max-width: 768px) {
            .welcome-header h1 {
                font-size: 2rem;
            }
            
            .welcome-header,
            .welcome-body,
            .welcome-footer {
                padding: 2rem 1.5rem;
            }
            
            .role-card {
                margin-bottom: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="welcome-container">
        <div class="floating-shapes">
            <div class="shape"></div>
            <div class="shape"></div>
            <div class="shape"></div>
        </div>
        
        <div class="welcome-card">
            <div class="welcome-header">
                <div class="mb-3">
                    <i class="fas fa-heartbeat fa-3x text-danger"></i>
                </div>
                <h1>Bienvenue sur Ilaji Medical</h1>
                <p>Système de gestion médicale moderne et sécurisé</p>
            </div>
            
            <div class="welcome-body">
                <div class="text-center mb-4">
                    <h3 class="text-primary mb-3">Choisissez votre profil</h3>
                    <p class="text-muted">Sélectionnez le type de compte qui correspond à votre rôle</p>
                </div>
                
                <div class="row g-4">
                    <div class="col-lg-3 col-md-6">
                        <div class="role-card admin" onclick="selectRole('admin')">
                            <i class="fas fa-user-shield role-icon admin"></i>
                            <h4 class="role-title">Administrateur</h4>
                            <p class="role-description">Gestion complète du système et des utilisateurs</p>
                            <ul class="role-features">
                                <li><i class="fas fa-check"></i> Gestion des utilisateurs</li>
                                <li><i class="fas fa-check"></i> Configuration système</li>
                                <li><i class="fas fa-check"></i> Rapports avancés</li>
                                <li><i class="fas fa-check"></i> Sauvegardes</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6">
                        <div class="role-card medecin" onclick="selectRole('medecin')">
                            <i class="fas fa-user-md role-icon medecin"></i>
                            <h4 class="role-title">Médecin</h4>
                            <p class="role-description">Consultation et suivi des patients</p>
                            <ul class="role-features">
                                <li><i class="fas fa-check"></i> Dossiers patients</li>
                                <li><i class="fas fa-check"></i> Consultations</li>
                                <li><i class="fas fa-check"></i> Prescriptions</li>
                                <li><i class="fas fa-check"></i> Planning médical</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6">
                        <div class="role-card secretaire" onclick="selectRole('secretaire')">
                            <i class="fas fa-user-tie role-icon secretaire"></i>
                            <h4 class="role-title">Secrétaire</h4>
                            <p class="role-description">Gestion des rendez-vous et accueil</p>
                            <ul class="role-features">
                                <li><i class="fas fa-check"></i> Rendez-vous</li>
                                <li><i class="fas fa-check"></i> Planning</li>
                                <li><i class="fas fa-check"></i> Patients</li>
                                <li><i class="fas fa-check"></i> Communications</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="col-lg-3 col-md-6">
                        <div class="role-card patient" onclick="selectRole('patient')">
                            <i class="fas fa-user role-icon patient"></i>
                            <h4 class="role-title">Patient</h4>
                            <p class="role-description">Accès à votre dossier médical</p>
                            <ul class="role-features">
                                <li><i class="fas fa-check"></i> Mon dossier</li>
                                <li><i class="fas fa-check"></i> Mes RDV</li>
                                <li><i class="fas fa-check"></i> Prescriptions</li>
                                <li><i class="fas fa-check"></i> Historique</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="welcome-footer">
                <p class="text-muted mb-0">
                    <small>&copy; 2025 Ilaji Medical. Système sécurisé et conforme RGPD.</small>
                </p>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function selectRole(role) {
            // Animation de sélection
            const card = document.querySelector(`.role-card.${role}`);
            card.style.transform = 'scale(0.95)';

            setTimeout(() => {
                card.style.transform = 'scale(1)';
                // Redirection vers la page de choix (connexion/inscription)
                window.location.href = `/auth/role-choice?role=${role}`;
            }, 150);
        }
        
        // Animation d'entrée pour les cartes
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.role-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                
                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 150);
            });
        });
    </script>
</body>
</html>
