from flask import Flask, render_template, request, redirect, url_for, flash, jsonify
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager, login_required, current_user
from flask_bcrypt import Bcrypt
from datetime import datetime, date, timedelta
import os

# Import de la configuration
from config import Config

# Import de l'instance db
from database import db

# Import des blueprints (temporairement désactivé)
# from auth import auth_bp

# Import des modèles (temporairement désactivé)
# from models.user import User, UserRole

# Import des décorateurs (temporairement désactivé)
# from decorators import login_required_with_role, admin_required, staff_required

# ===== STOCKAGE TEMPORAIRE EN MÉMOIRE =====
# (À remplacer par une vraie base de données)

medecins_data = [
    {
        'id': 1,
        'nom': '<PERSON>',
        'prenom': 'Dr. <PERSON>',
        'specialite': '<PERSON><PERSON><PERSON><PERSON>',
        'telephone': '01.23.45.67.89',
        'email': '<EMAIL>',
        'tarif_consultation': 25.00
    },
    {
        'id': 2,
        'nom': '<PERSON>rand',
        'prenom': 'Dr. <PERSON>',
        'specialite': 'Cardiologie',
        'telephone': '01.23.45.67.90',
        'email': '<EMAIL>',
        'tarif_consultation': 30.00
    }
]

patients_data = [
    {
        'id': 1,
        'nom': 'Ben Ali',
        'prenom': 'Ahmed',
        'cin': '12345678',
        'telephone': '***********.78',
        'email': '<EMAIL>'
    },
    {
        'id': 2,
        'nom': 'Alami',
        'prenom': 'Fatima',
        'cin': '87654321',
        'telephone': '***********.32',
        'email': '<EMAIL>'
    }
]

rdv_data = []

admins_data = [
    {
        'id': 1,
        'nom': 'Allaoui',
        'prenom': 'Mehdi',
        'username': 'mehdiallaoui',
        'email': '<EMAIL>',
        'telephone': '***********.78',
        'role': 'Administrateur Principal'
    },
    {
        'id': 2,
        'nom': 'Admin',
        'prenom': 'Système',
        'username': 'admin',
        'email': '<EMAIL>',
        'telephone': '***********.32',
        'role': 'Administrateur Système'
    }
]

def create_app():
    app = Flask(__name__)
    app.config.from_object(Config)

    # Initialisation de la base de données
    db.init_app(app)

    # Initialisation de Flask-Login (temporairement désactivé)
    # login_manager = LoginManager()
    # login_manager.init_app(app)
    # login_manager.login_view = 'auth.login'
    # login_manager.login_message = 'Vous devez être connecté pour accéder à cette page.'
    # login_manager.login_message_category = 'warning'

    # Initialisation de Bcrypt (temporairement désactivé)
    # bcrypt = Bcrypt()
    # bcrypt.init_app(app)

    # @login_manager.user_loader
    # def load_user(user_id):
    #     return User.query.get(int(user_id))

    # Enregistrement des blueprints (temporairement désactivé)
    # app.register_blueprint(auth_bp)

    # Création des tables si elles n'existent pas
    with app.app_context():
        db.create_all()

        # Création d'un utilisateur admin par défaut (temporairement désactivé)
        # admin_user = User.query.filter_by(email='<EMAIL>').first()
        # if not admin_user:
        #     admin_user = User(
        #         email='<EMAIL>',
        #         username='admin',
        #         nom='Administrateur',
        #         prenom='Système',
        #         role=UserRole.ADMIN,
        #         is_active=True,
        #         is_verified=True
        #     )
        #     admin_user.set_password('admin123')
        #     db.session.add(admin_user)
        #     db.session.commit()
        #     print("Utilisateur admin créé: <EMAIL> / admin123")

    return app

app = create_app()

@app.route('/')
def index():
    """Page d'accueil - redirection vers la page d'accueil"""
    return render_template('auth/welcome.html')

@app.route('/auth/role-choice')
def role_choice():
    """Page de choix entre connexion et inscription selon le rôle"""
    role = request.args.get('role', 'patient')
    # Vérifier que le rôle est valide
    valid_roles = ['admin', 'medecin', 'secretaire', 'patient']
    if role not in valid_roles:
        role = 'patient'
    return render_template('auth/role_choice.html', role=role)

@app.route('/auth/staff-login', methods=['GET', 'POST'])
def staff_login():
    """Page de connexion pour médecins et secrétaires"""
    role = request.args.get('role') or request.form.get('role', 'medecin')

    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')

        # Simulation de vérification des identifiants
        # TODO: Remplacer par une vraie vérification en base de données
        valid_credentials = {
            'medecin': {'dr.martin': 'medecin123', 'dr.durand': 'medecin456'},
            'secretaire': {'marie.sec': 'secret123', 'julie.admin': 'admin456'},
            'admin': {'mehdiallaoui': 'mehdi123'}
        }

        if role in valid_credentials and username in valid_credentials[role]:
            if valid_credentials[role][username] == password:
                dashboards = {
                    'admin': '/admin/dashboard',
                    'medecin': '/medecin/dashboard',
                    'secretaire': '/secretaire/dashboard'
                }
                flash(f'Connexion réussie ! Bienvenue {username}', 'success')
                return redirect(dashboards.get(role, '/dashboard'))
            else:
                flash('Mot de passe incorrect', 'error')
        else:
            flash('Nom d\'utilisateur non trouvé', 'error')

    return render_template('auth/staff_login.html', role=role)

@app.route('/auth/patient-access', methods=['GET', 'POST'])
def patient_access():
    """Page d'accès pour les patients avec CIN"""
    if request.method == 'POST':
        action = request.form.get('action')

        if action == 'login':
            cin = request.form.get('cin')

            # Simulation de vérification CIN
            # TODO: Remplacer par une vraie vérification en base de données
            valid_cins = ['12345678', '87654321', '11223344', '99887766']

            if cin in valid_cins:
                flash(f'Accès autorisé pour le CIN {cin}', 'success')
                return redirect('/patient/dashboard')
            else:
                flash('CIN non trouvé. Vérifiez votre numéro ou créez un nouveau dossier.', 'error')

        elif action == 'register':
            flash('Redirection vers la création de dossier patient', 'success')
            return redirect('/auth/patient-register')

    return render_template('auth/patient_access.html')

@app.route('/auth/patient-register')
def patient_register():
    """Page de création de dossier patient"""
    flash('Formulaire de création de dossier patient (à implémenter)', 'info')
    return redirect('/patient/dashboard')

@app.route('/dashboard')
def dashboard():
    """Tableau de bord principal (pour les utilisateurs connectés)"""
    try:
        # Pour l'instant, utilisons des données de démonstration
        # jusqu'à ce que les modèles soient corrigés
        total_patients = 0
        total_medecins = 0
        total_rdv_aujourd_hui = 0
        rdv_semaine = 0
        rdv_urgents = []
        prochains_rdv = []

        # Date actuelle formatée
        from datetime import datetime
        import locale
        try:
            locale.setlocale(locale.LC_TIME, 'fr_FR.UTF-8')
        except:
            pass  # Si la locale française n'est pas disponible

        date_actuelle = datetime.now().strftime('%A %d %B %Y')

        return render_template('index.html',
                             total_patients=total_patients,
                             total_medecins=total_medecins,
                             total_rdv_aujourd_hui=total_rdv_aujourd_hui,
                             rdv_semaine=rdv_semaine,
                             rdv_urgents=rdv_urgents,
                             prochains_rdv=prochains_rdv,
                             date_actuelle=date_actuelle)
    except Exception as e:
        flash(f'Erreur lors du chargement du tableau de bord: {str(e)}', 'error')
        return render_template('index.html',
                             total_patients=0,
                             total_medecins=0,
                             total_rdv_aujourd_hui=0,
                             rdv_semaine=0,
                             rdv_urgents=[],
                             prochains_rdv=[],
                             date_actuelle=datetime.now().strftime('%A %d %B %Y'))

# ===== DASHBOARDS PAR RÔLE =====

@app.route('/admin/dashboard')
def admin_dashboard():
    """Dashboard administrateur"""
    try:
        # Statistiques réelles basées sur les données en mémoire
        total_medecins = len(medecins_data)
        total_patients = len(patients_data)
        total_rdv = len(rdv_data)

        stats = {
            'total_users': total_medecins + total_patients + 1,  # +1 pour l'admin
            'total_medecins': total_medecins,
            'rdv_aujourd_hui': total_rdv,
            'revenus_mois': f'€{total_rdv * 25}',  # Simulation revenus
            'users_evolution': {
                'labels': ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Jun'],
                'data': [10, 15, 25, 30, 45, total_medecins + total_patients]
            },
            'roles_distribution': {
                'labels': ['Patients', 'Médecins', 'Secrétaires', 'Admins'],
                'data': [total_patients, total_medecins, 2, 1]
            }
        }

        recent_activities = []  # À implémenter
        system_alerts = []  # À implémenter

        return render_template('dashboards/admin.html',
                             stats=stats,
                             recent_activities=recent_activities,
                             system_alerts=system_alerts)
    except Exception as e:
        flash(f'Erreur lors du chargement du dashboard: {str(e)}', 'error')
        return render_template('dashboards/admin.html', stats={}, recent_activities=[], system_alerts=[])

@app.route('/medecin/dashboard')
def medecin_dashboard():
    """Dashboard médecin"""
    stats = {
        'mes_patients': 45,
        'consultations_jour': 8,
        'prescriptions_mois': 23,
        'urgences': 2,
        'users_evolution': {
            'labels': ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Jun'],
            'data': [5, 8, 12, 15, 18, 23]
        },
        'roles_distribution': {
            'labels': ['Consultations', 'Prescriptions', 'Examens', 'Urgences'],
            'data': [45, 23, 12, 8]
        }
    }

    # Activités récentes spécifiques au médecin
    recent_activities = [
        {
            'title': 'Consultation terminée',
            'description': 'Patient Ahmed Ben Ali - Consultation de routine',
            'timestamp': 'Il y a 15 minutes',
            'type_color': 'success',
            'icon': 'user-check'
        },
        {
            'title': 'Prescription émise',
            'description': 'Antibiotiques prescrits pour Fatima Zahra',
            'timestamp': 'Il y a 1 heure',
            'type_color': 'info',
            'icon': 'prescription'
        },
        {
            'title': 'Rendez-vous programmé',
            'description': 'Suivi post-opératoire - Mohamed Alami',
            'timestamp': 'Il y a 2 heures',
            'type_color': 'warning',
            'icon': 'calendar-plus'
        }
    ]

    return render_template('dashboards/medecin.html', stats=stats, recent_activities=recent_activities, system_alerts=[])

@app.route('/secretaire/dashboard')
def secretaire_dashboard():
    """Dashboard secrétaire"""
    stats = {
        'rdv_aujourd_hui': 12,
        'appels_jour': 28,
        'nouveaux_patients': 5,
        'en_attente': 3,
        'users_evolution': {
            'labels': ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Jun'],
            'data': [8, 12, 18, 22, 35, 40]
        },
        'roles_distribution': {
            'labels': ['RDV planifiés', 'RDV confirmés', 'RDV annulés', 'En attente'],
            'data': [15, 12, 2, 3]
        }
    }

    # Activités récentes spécifiques au secrétaire
    recent_activities = [
        {
            'title': 'RDV programmé',
            'description': 'Mme Fatima Benali - Dr. Martin - 16h00',
            'timestamp': 'Il y a 10 minutes',
            'type_color': 'success',
            'icon': 'calendar-plus'
        },
        {
            'title': 'Patient enregistré',
            'description': 'Nouveau patient: M. Youssef Alami',
            'timestamp': 'Il y a 30 minutes',
            'type_color': 'info',
            'icon': 'user-plus'
        },
        {
            'title': 'Appel traité',
            'description': 'Demande de renseignements - Mme Khadija',
            'timestamp': 'Il y a 45 minutes',
            'type_color': 'warning',
            'icon': 'phone'
        }
    ]

    return render_template('dashboards/secretaire.html', stats=stats, recent_activities=recent_activities, system_alerts=[])

@app.route('/patient/dashboard')
def patient_dashboard():
    """Dashboard patient"""
    stats = {
        'prochains_rdv': 2,
        'prescriptions_actives': 3,
        'resultats_attente': 1,
        'rappels': 2,
        'users_evolution': {
            'labels': ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Jun'],
            'data': [1, 2, 1, 3, 2, 4]
        },
        'roles_distribution': {
            'labels': ['Consultations', 'Prescriptions', 'Analyses', 'Vaccins'],
            'data': [8, 5, 3, 2]
        }
    }

    # Activités récentes spécifiques au patient
    recent_activities = [
        {
            'title': 'Rendez-vous confirmé',
            'description': 'RDV avec Dr. Martin le 25/12/2024 à 14h30',
            'timestamp': 'Il y a 2 heures',
            'type_color': 'success',
            'icon': 'calendar-check'
        },
        {
            'title': 'Résultats disponibles',
            'description': 'Analyses sanguines - Résultats normaux',
            'timestamp': 'Hier',
            'type_color': 'info',
            'icon': 'vial'
        },
        {
            'title': 'Prescription renouvelée',
            'description': 'Traitement hypertension - 30 jours',
            'timestamp': 'Il y a 3 jours',
            'type_color': 'warning',
            'icon': 'pills'
        }
    ]

    return render_template('dashboards/patient.html', stats=stats, recent_activities=recent_activities, system_alerts=[])

# ===== ROUTES PROTÉGÉES =====

@app.route('/patients')
def patients():
    """Liste des patients"""
    try:
        # Utiliser les vraies données des patients
        return render_template('patients/liste.html', patients=patients_data)
    except Exception as e:
        flash(f'Erreur lors du chargement des patients: {str(e)}', 'error')
        return render_template('patients/liste.html', patients=[])

# ===== ROUTES MÉDECIN =====

@app.route('/mes-patients')
def mes_patients():
    """Liste des patients du médecin connecté"""
    flash('Fonctionnalité en cours de développement', 'info')
    return render_template('en_developpement.html')

@app.route('/mon-planning')
def mon_planning():
    """Planning personnel du médecin"""
    flash('Fonctionnalité en cours de développement', 'info')
    return render_template('en_developpement.html')

@app.route('/mes-prescriptions')
def mes_prescriptions():
    """Prescriptions du médecin"""
    flash('Fonctionnalité en cours de développement', 'info')
    return render_template('en_developpement.html')

@app.route('/urgences')
def urgences():
    """Cas urgents"""
    flash('Fonctionnalité en cours de développement', 'info')
    return render_template('en_developpement.html')

@app.route('/nouvelle-consultation', methods=['GET', 'POST'])
def nouvelle_consultation():
    """Nouvelle consultation"""
    if request.method == 'POST':
        flash('Consultation enregistrée avec succès', 'success')
        return redirect('/medecin/dashboard')
    flash('Fonctionnalité en cours de développement', 'info')
    return render_template('en_developpement.html')

@app.route('/nouvelle-prescription', methods=['GET', 'POST'])
def nouvelle_prescription():
    """Nouvelle prescription"""
    if request.method == 'POST':
        flash('Prescription créée avec succès', 'success')
        return redirect('/medecin/dashboard')
    flash('Fonctionnalité en cours de développement', 'info')
    return render_template('en_developpement.html')

@app.route('/rechercher-patient', methods=['GET', 'POST'])
def rechercher_patient():
    """Rechercher un patient"""
    if request.method == 'POST':
        flash('Recherche effectuée', 'info')
    flash('Fonctionnalité en cours de développement', 'info')
    return render_template('en_developpement.html')

@app.route('/mon-agenda')
def mon_agenda():
    """Agenda personnel du médecin"""
    flash('Fonctionnalité en cours de développement', 'info')
    return render_template('en_developpement.html')

# ===== ROUTES PATIENT =====

@app.route('/mes-rendez-vous')
def mes_rendez_vous():
    """Rendez-vous du patient"""
    flash('Fonctionnalité en cours de développement', 'info')
    return render_template('en_developpement.html')

@app.route('/mes-resultats')
def mes_resultats():
    """Résultats d'analyses du patient"""
    flash('Fonctionnalité en cours de développement', 'info')
    return render_template('en_developpement.html')

@app.route('/mes-rappels')
def mes_rappels():
    """Rappels du patient"""
    flash('Fonctionnalité en cours de développement', 'info')
    return render_template('en_developpement.html')

@app.route('/prendre-rdv', methods=['GET', 'POST'])
def prendre_rdv():
    """Prendre un rendez-vous"""
    if request.method == 'POST':
        flash('Demande de rendez-vous envoyée avec succès', 'success')
        return redirect('/patient/dashboard')
    flash('Fonctionnalité en cours de développement', 'info')
    return render_template('en_developpement.html')

@app.route('/mon-dossier')
def mon_dossier():
    """Dossier médical complet du patient"""
    flash('Fonctionnalité en cours de développement', 'info')
    return render_template('en_developpement.html')

@app.route('/mes-documents')
def mes_documents():
    """Documents du patient"""
    flash('Fonctionnalité en cours de développement', 'info')
    return render_template('en_developpement.html')

@app.route('/contact-medecin', methods=['GET', 'POST'])
def contact_medecin():
    """Contacter le médecin"""
    if request.method == 'POST':
        flash('Message envoyé au médecin avec succès', 'success')
        return redirect('/patient/dashboard')
    flash('Fonctionnalité en cours de développement', 'info')
    return render_template('en_developpement.html')

# ===== ROUTES SECRÉTAIRE =====

@app.route('/planning-jour')
def planning_jour():
    """Planning du jour pour secrétaire"""
    flash('Fonctionnalité en cours de développement', 'info')
    return render_template('en_developpement.html')

@app.route('/journal-appels')
def journal_appels():
    """Journal des appels"""
    flash('Fonctionnalité en cours de développement', 'info')
    return render_template('en_developpement.html')

@app.route('/nouveaux-patients')
def nouveaux_patients():
    """Liste des nouveaux patients"""
    flash('Fonctionnalité en cours de développement', 'info')
    return render_template('en_developpement.html')

@app.route('/salle-attente')
def salle_attente():
    """Gestion de la salle d'attente"""
    flash('Fonctionnalité en cours de développement', 'info')
    return render_template('en_developpement.html')

@app.route('/nouveau-rdv', methods=['GET', 'POST'])
def nouveau_rdv():
    """Nouveau rendez-vous (secrétaire)"""
    if request.method == 'POST':
        flash('Rendez-vous créé avec succès', 'success')
        return redirect('/secretaire/dashboard')
    flash('Fonctionnalité en cours de développement', 'info')
    return render_template('en_developpement.html')

@app.route('/enregistrer-patient', methods=['GET', 'POST'])
def enregistrer_patient():
    """Enregistrer un nouveau patient"""
    if request.method == 'POST':
        flash('Patient enregistré avec succès', 'success')
        return redirect('/secretaire/dashboard')
    flash('Fonctionnalité en cours de développement', 'info')
    return render_template('en_developpement.html')

@app.route('/gestion-planning')
def gestion_planning():
    """Gestion du planning général"""
    flash('Fonctionnalité en cours de développement', 'info')
    return render_template('en_developpement.html')

@app.route('/facturation')
def facturation():
    """Gestion de la facturation"""
    flash('Fonctionnalité en cours de développement', 'info')
    return render_template('en_developpement.html')

@app.route('/medecins')
def medecins():
    """Liste des médecins"""
    try:
        # Utiliser les vraies données des médecins
        return render_template('medecins/liste.html', medecins=medecins_data)
    except Exception as e:
        flash(f'Erreur lors du chargement des médecins: {str(e)}', 'error')
        return render_template('medecins/liste.html', medecins=[])

@app.route('/rendez-vous')
def rendez_vous():
    """Liste des rendez-vous"""
    try:
        # Pour l'instant, liste vide jusqu'à correction des modèles
        rdv = []
        return render_template('rendez_vous/liste.html', rendez_vous=rdv)
    except Exception as e:
        flash(f'Erreur lors du chargement des rendez-vous: {str(e)}', 'error')
        return render_template('rendez_vous/liste.html', rendez_vous=[])

@app.route('/planning')
def planning():
    """Planning du jour"""
    try:
        # Pour l'instant, liste vide jusqu'à correction des modèles
        aujourd_hui = date.today()
        rdv_jour = []
        return render_template('rendez_vous/planning.html', rendez_vous=rdv_jour, date=aujourd_hui)
    except Exception as e:
        flash(f'Erreur lors du chargement du planning: {str(e)}', 'error')
        return render_template('rendez_vous/planning.html', rendez_vous=[], date=aujourd_hui)

@app.route('/nouveau-patient', methods=['GET', 'POST'])
def nouveau_patient():
    """Formulaire nouveau patient"""
    if request.method == 'POST':
        # Récupérer les données du formulaire
        nom = request.form.get('nom')
        prenom = request.form.get('prenom')
        cin = request.form.get('cin')
        telephone = request.form.get('telephone')
        email = request.form.get('email')
        date_naissance = request.form.get('date_naissance')
        adresse = request.form.get('adresse')
        profession = request.form.get('profession')

        # Générer un nouvel ID
        nouveau_id = max([p['id'] for p in patients_data], default=0) + 1

        # Créer le nouveau patient
        nouveau_patient = {
            'id': nouveau_id,
            'nom': nom,
            'prenom': prenom,
            'cin': cin,
            'telephone': telephone,
            'email': email,
            'date_naissance': date_naissance,
            'adresse': adresse,
            'profession': profession
        }

        # Ajouter aux données en mémoire
        patients_data.append(nouveau_patient)

        flash(f'Patient {prenom} {nom} ajouté avec succès ! Total patients: {len(patients_data)}', 'success')
        return redirect('/admin/dashboard')

    return render_template('patients/nouveau.html')

@app.route('/nouveau-medecin', methods=['GET', 'POST'])
def nouveau_medecin():
    """Formulaire nouveau médecin"""
    if request.method == 'POST':
        # Récupérer les données du formulaire
        nom = request.form.get('nom')
        prenom = request.form.get('prenom')
        specialite = request.form.get('specialite')
        telephone = request.form.get('telephone')
        email = request.form.get('email')
        tarif_consultation = request.form.get('tarif_consultation')
        adresse = request.form.get('adresse')
        numero_rpps = request.form.get('numero_rpps')
        numero_adeli = request.form.get('numero_adeli')
        secteur_conventionnement = request.form.get('secteur_conventionnement')

        # Générer un nouvel ID
        nouveau_id = max([m['id'] for m in medecins_data], default=0) + 1

        # Créer le nouveau médecin
        nouveau_medecin = {
            'id': nouveau_id,
            'nom': nom,
            'prenom': prenom,
            'specialite': specialite,
            'telephone': telephone,
            'email': email,
            'tarif_consultation': float(tarif_consultation) if tarif_consultation else 0.0,
            'adresse': adresse,
            'numero_rpps': numero_rpps,
            'numero_adeli': numero_adeli,
            'secteur_conventionnement': secteur_conventionnement
        }

        # Ajouter aux données en mémoire
        medecins_data.append(nouveau_medecin)

        flash(f'Médecin {prenom} {nom} ajouté avec succès ! Total médecins: {len(medecins_data)}', 'success')
        return redirect('/admin/dashboard')

    return render_template('medecins/nouveau.html')

@app.route('/nouveau-rendez-vous', methods=['GET', 'POST'])
def nouveau_rendez_vous():
    """Formulaire nouveau rendez-vous"""
    if request.method == 'POST':
        # Récupérer les données du formulaire
        patient_id = request.form.get('patient_id')
        medecin_id = request.form.get('medecin_id')
        date_rdv = request.form.get('date_rdv')
        heure_rdv = request.form.get('heure_rdv')
        motif = request.form.get('motif')

        # Pour l'instant, on simule l'ajout (à remplacer par vraie BDD)
        flash(f'Rendez-vous programmé avec succès pour le {date_rdv} à {heure_rdv}', 'success')
        return redirect('/admin/dashboard')

    try:
        # Pour l'instant, listes vides jusqu'à correction des modèles
        patients = []
        medecins = []
        return render_template('rendez_vous/nouveau.html', patients=patients, medecins=medecins)
    except Exception as e:
        flash(f'Erreur lors du chargement du formulaire: {str(e)}', 'error')
        return render_template('rendez_vous/nouveau.html', patients=[], medecins=[])

# ===== ROUTES ADMIN SPÉCIALISÉES =====

@app.route('/admin/utilisateurs')
def admin_utilisateurs():
    """Gestion des utilisateurs (admin)"""
    tous_utilisateurs = []

    # Ajouter les administrateurs
    for admin in admins_data:
        tous_utilisateurs.append({
            'id': admin['id'],
            'nom': admin['nom'],
            'prenom': admin['prenom'],
            'email': admin['email'],
            'telephone': admin['telephone'],
            'type': 'Administrateur',
            'role': admin['role'],
            'username': admin['username']
        })

    # Ajouter les médecins
    for medecin in medecins_data:
        tous_utilisateurs.append({
            'id': medecin['id'],
            'nom': medecin['nom'],
            'prenom': medecin['prenom'],
            'email': medecin['email'],
            'telephone': medecin['telephone'],
            'type': 'Médecin',
            'specialite': medecin['specialite']
        })

    # Ajouter les patients
    for patient in patients_data:
        tous_utilisateurs.append({
            'id': patient['id'],
            'nom': patient['nom'],
            'prenom': patient['prenom'],
            'email': patient['email'],
            'telephone': patient['telephone'],
            'type': 'Patient',
            'cin': patient['cin']
        })

    return render_template('admin/utilisateurs.html', utilisateurs=tous_utilisateurs)

@app.route('/admin/medecins')
def admin_medecins():
    """Gestion des médecins (admin)"""
    return render_template('admin/medecins.html', medecins=medecins_data)

@app.route('/admin/planning')
def admin_planning():
    """Planning hebdomadaire (admin)"""
    from datetime import datetime, timedelta

    # Générer les 7 jours de la semaine
    aujourd_hui = datetime.now()
    debut_semaine = aujourd_hui - timedelta(days=aujourd_hui.weekday())

    planning_semaine = []
    for i in range(7):
        jour = debut_semaine + timedelta(days=i)
        planning_semaine.append({
            'date': jour.strftime('%Y-%m-%d'),
            'jour': jour.strftime('%A'),
            'jour_fr': ['Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi', 'Samedi', 'Dimanche'][i],
            'rdv': []  # À remplir avec les vrais RDV
        })

    return render_template('admin/planning.html', planning_semaine=planning_semaine)

@app.route('/admin/revenus')
def admin_revenus():
    """Détails des revenus (admin)"""
    revenus_details = {
        'total_mois': len(rdv_data) * 25,
        'total_consultations': len(rdv_data),
        'revenus_par_medecin': [],
        'evolution_mensuelle': []
    }

    return render_template('admin/revenus.html', revenus=revenus_details)

@app.route('/admin/creer-utilisateur')
def admin_creer_utilisateur():
    """Créer un nouvel utilisateur"""
    return render_template('admin/creer_utilisateur.html')

@app.route('/admin/parametres')
def admin_parametres():
    """Paramètres système"""
    flash('Fonctionnalité en cours de développement', 'info')
    return render_template('en_developpement.html')

@app.route('/admin/sauvegarde')
def admin_sauvegarde():
    """Sauvegarde système"""
    flash('Fonctionnalité en cours de développement', 'info')
    return render_template('en_developpement.html')

@app.route('/admin/logs')
def admin_logs():
    """Logs système"""
    flash('Fonctionnalité en cours de développement', 'info')
    return render_template('en_developpement.html')

# ===== ROUTES CRUD MÉDECINS =====

@app.route('/admin/medecin/<int:medecin_id>')
def admin_medecin_details(medecin_id):
    """Détails d'un médecin"""
    medecin = next((m for m in medecins_data if m['id'] == medecin_id), None)
    if not medecin:
        flash('Médecin non trouvé', 'error')
        return redirect('/admin/medecins')

    return render_template('admin/medecin_details.html', medecin=medecin)

@app.route('/admin/medecin/<int:medecin_id>/modifier', methods=['GET', 'POST'])
def admin_medecin_modifier(medecin_id):
    """Modifier un médecin"""
    medecin = next((m for m in medecins_data if m['id'] == medecin_id), None)
    if not medecin:
        flash('Médecin non trouvé', 'error')
        return redirect('/admin/medecins')

    if request.method == 'POST':
        # Mettre à jour les données
        medecin['nom'] = request.form.get('nom')
        medecin['prenom'] = request.form.get('prenom')
        medecin['specialite'] = request.form.get('specialite')
        medecin['telephone'] = request.form.get('telephone')
        medecin['email'] = request.form.get('email')
        medecin['tarif_consultation'] = float(request.form.get('tarif_consultation', 0))
        medecin['adresse'] = request.form.get('adresse')
        medecin['numero_rpps'] = request.form.get('numero_rpps')
        medecin['numero_adeli'] = request.form.get('numero_adeli')
        medecin['secteur_conventionnement'] = request.form.get('secteur_conventionnement')

        flash(f'Médecin {medecin["prenom"]} {medecin["nom"]} modifié avec succès !', 'success')
        return redirect('/admin/medecins')

    return render_template('admin/medecin_modifier.html', medecin=medecin)

@app.route('/admin/medecin/<int:medecin_id>/supprimer')
def admin_medecin_supprimer(medecin_id):
    """Supprimer un médecin"""
    medecin = next((m for m in medecins_data if m['id'] == medecin_id), None)
    if not medecin:
        flash('Médecin non trouvé', 'error')
        return redirect('/admin/medecins')

    # Supprimer le médecin de la liste
    medecins_data.remove(medecin)

    flash(f'Médecin {medecin["prenom"]} {medecin["nom"]} supprimé avec succès !', 'success')
    return redirect('/admin/medecins')

# ===== ROUTES CRUD PATIENTS =====

@app.route('/admin/patient/<int:patient_id>')
def admin_patient_details(patient_id):
    """Détails d'un patient"""
    patient = next((p for p in patients_data if p['id'] == patient_id), None)
    if not patient:
        flash('Patient non trouvé', 'error')
        return redirect('/admin/utilisateurs')

    return render_template('admin/patient_details.html', patient=patient)

@app.route('/admin/patient/<int:patient_id>/modifier', methods=['GET', 'POST'])
def admin_patient_modifier(patient_id):
    """Modifier un patient"""
    patient = next((p for p in patients_data if p['id'] == patient_id), None)
    if not patient:
        flash('Patient non trouvé', 'error')
        return redirect('/admin/utilisateurs')

    if request.method == 'POST':
        # Mettre à jour les données
        patient['nom'] = request.form.get('nom')
        patient['prenom'] = request.form.get('prenom')
        patient['cin'] = request.form.get('cin')
        patient['telephone'] = request.form.get('telephone')
        patient['email'] = request.form.get('email')
        patient['date_naissance'] = request.form.get('date_naissance')
        patient['adresse'] = request.form.get('adresse')
        patient['profession'] = request.form.get('profession')

        flash(f'Patient {patient["prenom"]} {patient["nom"]} modifié avec succès !', 'success')
        return redirect('/admin/utilisateurs')

    return render_template('admin/patient_modifier.html', patient=patient)

@app.route('/admin/patient/<int:patient_id>/supprimer')
def admin_patient_supprimer(patient_id):
    """Supprimer un patient"""
    patient = next((p for p in patients_data if p['id'] == patient_id), None)
    if not patient:
        flash('Patient non trouvé', 'error')
        return redirect('/admin/utilisateurs')

    # Supprimer le patient de la liste
    patients_data.remove(patient)

    flash(f'Patient {patient["prenom"]} {patient["nom"]} supprimé avec succès !', 'success')
    return redirect('/admin/utilisateurs')

# ===== ROUTES CRUD UTILISATEURS GÉNÉRIQUES =====

@app.route('/admin/utilisateur/<int:user_id>')
def admin_utilisateur_details(user_id):
    """Détails d'un utilisateur (admin, médecin ou patient)"""
    # Chercher dans les admins
    for admin in admins_data:
        if admin['id'] == user_id:
            return render_template('admin/admin_details.html', utilisateur=admin, type='admin')

    # Chercher dans les médecins
    for medecin in medecins_data:
        if medecin['id'] == user_id:
            return render_template('admin/medecin_details.html', medecin=medecin)

    # Chercher dans les patients
    for patient in patients_data:
        if patient['id'] == user_id:
            return render_template('admin/patient_details.html', patient=patient)

    flash('Utilisateur non trouvé', 'error')
    return redirect('/admin/utilisateurs')

@app.route('/admin/utilisateur/<int:user_id>/modifier', methods=['GET', 'POST'])
def admin_utilisateur_modifier(user_id):
    """Modifier un utilisateur (redirection selon le type)"""
    # Chercher dans les admins
    for admin in admins_data:
        if admin['id'] == user_id:
            if request.method == 'POST':
                admin['nom'] = request.form.get('nom')
                admin['prenom'] = request.form.get('prenom')
                admin['email'] = request.form.get('email')
                admin['telephone'] = request.form.get('telephone')
                admin['role'] = request.form.get('role')
                flash(f'Administrateur {admin["prenom"]} {admin["nom"]} modifié avec succès !', 'success')
                return redirect('/admin/utilisateurs')
            return render_template('admin/admin_modifier.html', admin=admin)

    # Chercher dans les médecins
    for medecin in medecins_data:
        if medecin['id'] == user_id:
            return redirect(f'/admin/medecin/{user_id}/modifier')

    # Chercher dans les patients
    for patient in patients_data:
        if patient['id'] == user_id:
            return redirect(f'/admin/patient/{user_id}/modifier')

    flash('Utilisateur non trouvé', 'error')
    return redirect('/admin/utilisateurs')

@app.route('/admin/utilisateur/<int:user_id>/supprimer')
def admin_utilisateur_supprimer(user_id):
    """Supprimer un utilisateur"""
    # Chercher dans les admins
    for admin in admins_data:
        if admin['id'] == user_id:
            if len(admins_data) <= 1:
                flash('Impossible de supprimer le dernier administrateur !', 'error')
                return redirect('/admin/utilisateurs')
            admins_data.remove(admin)
            flash(f'Administrateur {admin["prenom"]} {admin["nom"]} supprimé avec succès !', 'success')
            return redirect('/admin/utilisateurs')

    # Chercher dans les médecins
    for medecin in medecins_data:
        if medecin['id'] == user_id:
            return redirect(f'/admin/medecin/{user_id}/supprimer')

    # Chercher dans les patients
    for patient in patients_data:
        if patient['id'] == user_id:
            return redirect(f'/admin/patient/{user_id}/supprimer')

    flash('Utilisateur non trouvé', 'error')
    return redirect('/admin/utilisateurs')

# ===== ROUTES UTILITAIRES ADMIN =====

@app.route('/admin/reset-password/<int:user_id>', methods=['GET', 'POST'])
def admin_reset_password(user_id):
    """Réinitialiser le mot de passe d'un utilisateur"""
    # Chercher l'utilisateur dans tous les types
    user = None
    user_type = None

    # Chercher dans les admins
    for admin in admins_data:
        if admin['id'] == user_id:
            user = admin
            user_type = 'admin'
            break

    # Chercher dans les médecins
    if not user:
        for medecin in medecins_data:
            if medecin['id'] == user_id:
                user = medecin
                user_type = 'medecin'
                break

    # Chercher dans les patients
    if not user:
        for patient in patients_data:
            if patient['id'] == user_id:
                user = patient
                user_type = 'patient'
                break

    if not user:
        flash('Utilisateur non trouvé', 'error')
        return redirect('/admin/utilisateurs')

    if request.method == 'POST':
        nouveau_password = request.form.get('nouveau_password')
        confirmer_password = request.form.get('confirmer_password')

        if nouveau_password != confirmer_password:
            flash('Les mots de passe ne correspondent pas', 'error')
            return render_template('admin/reset_password.html', user=user, user_type=user_type)

        if len(nouveau_password) < 6:
            flash('Le mot de passe doit contenir au moins 6 caractères', 'error')
            return render_template('admin/reset_password.html', user=user, user_type=user_type)

        # Simuler la mise à jour du mot de passe
        flash(f'Mot de passe réinitialisé avec succès pour {user["prenom"]} {user["nom"]}', 'success')
        return redirect(f'/admin/utilisateur/{user_id}')

    return render_template('admin/reset_password.html', user=user, user_type=user_type)

@app.route('/admin/historique-connexions/<int:user_id>')
def admin_historique_connexions(user_id):
    """Historique des connexions d'un utilisateur"""
    # Chercher l'utilisateur dans tous les types
    user = None
    user_type = None

    # Chercher dans les admins
    for admin in admins_data:
        if admin['id'] == user_id:
            user = admin
            user_type = 'admin'
            break

    # Chercher dans les médecins
    if not user:
        for medecin in medecins_data:
            if medecin['id'] == user_id:
                user = medecin
                user_type = 'medecin'
                break

    # Chercher dans les patients
    if not user:
        for patient in patients_data:
            if patient['id'] == user_id:
                user = patient
                user_type = 'patient'
                break

    if not user:
        flash('Utilisateur non trouvé', 'error')
        return redirect('/admin/utilisateurs')

    # Simuler des données d'historique
    historique = [
        {
            'date': '2025-06-22',
            'heure': '14:30:15',
            'ip': '*************',
            'navigateur': 'Chrome 120.0',
            'statut': 'Succès',
            'action': 'Connexion'
        },
        {
            'date': '2025-06-22',
            'heure': '09:15:42',
            'ip': '*************',
            'navigateur': 'Chrome 120.0',
            'statut': 'Succès',
            'action': 'Connexion'
        },
        {
            'date': '2025-06-21',
            'heure': '16:45:23',
            'ip': '*************',
            'navigateur': 'Chrome 120.0',
            'statut': 'Succès',
            'action': 'Déconnexion'
        },
        {
            'date': '2025-06-21',
            'heure': '08:20:10',
            'ip': '*************',
            'navigateur': 'Chrome 120.0',
            'statut': 'Succès',
            'action': 'Connexion'
        },
        {
            'date': '2025-06-20',
            'heure': '17:30:55',
            'ip': '************',
            'navigateur': 'Firefox 121.0',
            'statut': 'Échec',
            'action': 'Tentative connexion'
        }
    ]

    return render_template('admin/historique_connexions.html', user=user, user_type=user_type, historique=historique)

@app.route('/admin/planning-medecin/<int:medecin_id>')
def admin_planning_medecin(medecin_id):
    """Voir le planning d'un médecin"""
    medecin = next((m for m in medecins_data if m['id'] == medecin_id), None)
    if not medecin:
        flash('Médecin non trouvé', 'error')
        return redirect('/admin/medecins')

    flash(f'Planning de Dr. {medecin["prenom"]} {medecin["nom"]} - Fonctionnalité en cours de développement', 'info')
    return render_template('en_developpement.html')

@app.route('/admin/statistiques-medecin/<int:medecin_id>')
def admin_statistiques_medecin(medecin_id):
    """Statistiques d'un médecin"""
    medecin = next((m for m in medecins_data if m['id'] == medecin_id), None)
    if not medecin:
        flash('Médecin non trouvé', 'error')
        return redirect('/admin/medecins')

    flash(f'Statistiques de Dr. {medecin["prenom"]} {medecin["nom"]} - Fonctionnalité en cours de développement', 'info')
    return render_template('en_developpement.html')

@app.route('/admin/dossier-patient/<int:patient_id>')
def admin_dossier_patient(patient_id):
    """Dossier médical d'un patient"""
    patient = next((p for p in patients_data if p['id'] == patient_id), None)
    if not patient:
        flash('Patient non trouvé', 'error')
        return redirect('/admin/utilisateurs')

    flash(f'Dossier médical de {patient["prenom"]} {patient["nom"]} - Fonctionnalité en cours de développement', 'info')
    return render_template('en_developpement.html')

@app.route('/admin/prescriptions-patient/<int:patient_id>')
def admin_prescriptions_patient(patient_id):
    """Prescriptions d'un patient"""
    patient = next((p for p in patients_data if p['id'] == patient_id), None)
    if not patient:
        flash('Patient non trouvé', 'error')
        return redirect('/admin/utilisateurs')

    flash(f'Prescriptions de {patient["prenom"]} {patient["nom"]} - Fonctionnalité en cours de développement', 'info')
    return render_template('en_developpement.html')

@app.route('/admin/export-revenus')
def admin_export_revenus():
    """Exporter les revenus en Excel"""
    flash('Fonctionnalité en cours de développement', 'info')
    return render_template('en_developpement.html')

@app.route('/admin/rapport-mensuel')
def admin_rapport_mensuel():
    """Générer rapport mensuel PDF"""
    flash('Fonctionnalité en cours de développement', 'info')
    return render_template('en_developpement.html')

@app.route('/admin/facturation')
def admin_facturation():
    """Gestion de la facturation"""
    flash('Fonctionnalité en cours de développement', 'info')
    return render_template('en_developpement.html')

@app.route('/admin/parametres-tarifs')
def admin_parametres_tarifs():
    """Paramètres des tarifs"""
    flash('Fonctionnalité en cours de développement', 'info')
    return render_template('en_developpement.html')

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)