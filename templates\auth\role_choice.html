<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Connexion {{ role.title() }} - <PERSON><PERSON><PERSON></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #2563eb;
            --primary-dark: #1d4ed8;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --info-color: #06b6d4;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 0;
        }
        
        .choice-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem 1rem;
        }
        
        .choice-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
            max-width: 600px;
            width: 100%;
        }
        
        .choice-header {
            background: linear-gradient(135deg, 
                {% if role == 'admin' %}var(--danger-color){% elif role == 'medecin' %}var(--success-color){% elif role == 'secretaire' %}var(--warning-color){% else %}var(--info-color){% endif %} 0%, 
                {% if role == 'admin' %}#dc2626{% elif role == 'medecin' %}#059669{% elif role == 'secretaire' %}#d97706{% else %}#0891b2{% endif %} 100%);
            color: white;
            padding: 3rem 2rem;
            text-align: center;
        }
        
        .role-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
            display: block;
        }
        
        .choice-header h1 {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            color: white;
        }
        
        .choice-header p {
            font-size: 1.1rem;
            opacity: 0.9;
            margin: 0;
        }
        
        .choice-body {
            padding: 3rem 2rem;
        }
        
        .choice-option {
            background: white;
            border: 2px solid #e2e8f0;
            border-radius: 15px;
            padding: 2rem;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            margin-bottom: 1.5rem;
            position: relative;
            overflow: hidden;
        }
        
        .choice-option:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px -5px rgba(0, 0, 0, 0.15);
            border-color: {% if role == 'admin' %}var(--danger-color){% elif role == 'medecin' %}var(--success-color){% elif role == 'secretaire' %}var(--warning-color){% else %}var(--info-color){% endif %};
        }
        
        .choice-option.signin {
            border-color: var(--success-color);
        }
        
        .choice-option.signin:hover {
            border-color: var(--success-color);
            box-shadow: 0 15px 35px -5px rgba(16, 185, 129, 0.3);
        }
        
        .choice-option.signup {
            border-color: var(--primary-color);
        }
        
        .choice-option.signup:hover {
            border-color: var(--primary-color);
            box-shadow: 0 15px 35px -5px rgba(37, 99, 235, 0.3);
        }
        
        .option-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            display: block;
        }
        
        .option-icon.signin { color: var(--success-color); }
        .option-icon.signup { color: var(--primary-color); }
        
        .option-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #1f2937;
        }
        
        .option-description {
            color: #6b7280;
            font-size: 1rem;
            line-height: 1.6;
            margin-bottom: 1.5rem;
        }
        
        .btn-choice {
            border: none;
            border-radius: 10px;
            color: white;
            padding: 0.75rem 2rem;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            margin-top: 1rem;
        }
        
        .btn-signin {
            background: linear-gradient(135deg, var(--success-color) 0%, #059669 100%);
        }
        
        .btn-signup {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
        }
        
        .btn-choice:hover {
            transform: translateY(-2px);
            color: white;
        }
        
        .btn-signin:hover {
            box-shadow: 0 10px 25px -5px rgba(16, 185, 129, 0.4);
        }
        
        .btn-signup:hover {
            box-shadow: 0 10px 25px -5px rgba(37, 99, 235, 0.4);
        }
        
        .back-link {
            text-align: center;
            margin-top: 2rem;
        }
        
        .back-link a {
            color: #6b7280;
            text-decoration: none;
            font-size: 0.9rem;
            transition: color 0.3s ease;
        }
        
        .back-link a:hover {
            color: #374151;
        }
        
        .floating-shapes {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: -1;
        }
        
        .shape {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }
        
        .shape:nth-child(1) {
            width: 80px;
            height: 80px;
            top: 10%;
            left: 10%;
            animation-delay: 0s;
        }
        
        .shape:nth-child(2) {
            width: 120px;
            height: 120px;
            top: 70%;
            right: 10%;
            animation-delay: 2s;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
        
        @media (max-width: 768px) {
            .choice-header h1 {
                font-size: 1.5rem;
            }
            
            .choice-header,
            .choice-body {
                padding: 2rem 1.5rem;
            }
            
            .choice-option {
                margin-bottom: 1rem;
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="choice-container">
        <div class="floating-shapes">
            <div class="shape"></div>
            <div class="shape"></div>
        </div>
        
        <div class="choice-card">
            <div class="choice-header">
                <i class="fas fa-{% if role == 'admin' %}user-shield{% elif role == 'medecin' %}user-md{% elif role == 'secretaire' %}user-tie{% else %}user{% endif %} role-icon"></i>
                <h1>Espace {{ role.title() }}</h1>
                <p>
                    {% if role == 'admin' %}Administration du système Ilaji Medical
                    {% elif role == 'medecin' %}Espace professionnel de santé
                    {% elif role == 'secretaire' %}Gestion administrative et planning
                    {% else %}Votre dossier médical personnel{% endif %}
                </p>
            </div>
            
            <div class="choice-body">
                <div class="text-center mb-4">
                    <h3 class="text-dark mb-3">Comment souhaitez-vous continuer ?</h3>
                    <p class="text-muted">Choisissez l'option qui correspond à votre situation</p>
                </div>
                
                <div class="choice-option signin" onclick="goToSignIn()">
                    <i class="fas fa-{% if role == 'patient' %}id-card{% else %}sign-in-alt{% endif %} option-icon signin"></i>
                    <h4 class="option-title">
                        {% if role == 'patient' %}J'ai déjà un dossier{% else %}J'ai déjà un compte{% endif %}
                    </h4>
                    <p class="option-description">
                        {% if role == 'patient' %}
                            Accédez à votre dossier médical avec votre numéro CIN.
                        {% else %}
                            Connectez-vous avec vos identifiants professionnels.
                        {% endif %}
                    </p>
                    <a href="#" class="btn btn-signin btn-choice">
                        <i class="fas fa-{% if role == 'patient' %}id-card{% else %}sign-in-alt{% endif %} me-2"></i>
                        {% if role == 'patient' %}Accéder à mon dossier{% else %}Se connecter{% endif %}
                    </a>
                </div>

                <div class="choice-option signup" onclick="goToSignUp()">
                    <i class="fas fa-user-plus option-icon signup"></i>
                    <h4 class="option-title">
                        {% if role == 'patient' %}Première visite{% else %}Créer un compte{% endif %}
                    </h4>
                    <p class="option-description">
                        {% if role == 'patient' %}
                            Créez votre dossier patient pour votre première consultation.
                        {% elif role == 'admin' %}
                            Seul un administrateur peut créer des comptes admin.
                        {% else %}
                            Les comptes personnel sont créés par l'administration.
                        {% endif %}
                    </p>
                    <a href="#" class="btn btn-signup btn-choice">
                        <i class="fas fa-user-plus me-2"></i>
                        {% if role == 'patient' %}Créer mon dossier{% else %}Demander un compte{% endif %}
                    </a>
                </div>
                
                <div class="back-link">
                    <a href="/">
                        <i class="fas fa-arrow-left me-1"></i>Retour à la sélection de rôle
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        const role = '{{ role }}';
        
        function goToSignIn() {
            // Animation de sélection
            const card = document.querySelector('.choice-option.signin');
            card.style.transform = 'scale(0.95)';

            setTimeout(() => {
                card.style.transform = 'scale(1)';
                // Redirection selon le type d'utilisateur dans la même fenêtre
                if (role === 'patient') {
                    window.location.replace(`/auth/patient-access`);
                } else {
                    window.location.replace(`/auth/staff-login?role=${role}`);
                }
            }, 150);
        }

        function goToSignUp() {
            // Animation de sélection
            const card = document.querySelector('.choice-option.signup');
            card.style.transform = 'scale(0.95)';

            setTimeout(() => {
                card.style.transform = 'scale(1)';
                // Redirection selon le type d'utilisateur dans la même fenêtre
                if (role === 'patient') {
                    window.location.replace(`/auth/patient-access`);
                } else {
                    // Pour le personnel, pas d'inscription directe
                    alert('Les comptes personnel sont créés par l\'administrateur. Contactez votre responsable.');
                }
            }, 150);
        }
        
        // Animation d'entrée pour les options
        document.addEventListener('DOMContentLoaded', function() {
            const options = document.querySelectorAll('.choice-option');
            options.forEach((option, index) => {
                option.style.opacity = '0';
                option.style.transform = 'translateY(30px)';
                
                setTimeout(() => {
                    option.style.transition = 'all 0.6s ease';
                    option.style.opacity = '1';
                    option.style.transform = 'translateY(0)';
                }, index * 200);
            });
        });
    </script>
</body>
</html>
