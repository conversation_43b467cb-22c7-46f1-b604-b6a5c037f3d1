{% extends "base.html" %}

{% block title %}<PERSON><PERSON>er Compte Personnel - Ilaji Medical{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="display-6 text-primary mb-1">
                    <i class="fas fa-user-plus me-3"></i>Créer Compte Personnel
                </h1>
                <p class="text-muted mb-0">Création de comptes pour médecins et secrétaires</p>
            </div>
            <div>
                <a href="/admin/gestion-personnel" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Retour Gestion Personnel
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <!-- Sélection du type de compte -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-gradient-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-users me-2"></i>Type de compte à créer
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <div class="card border-success h-100" onclick="selectionnerType('medecin')">
                            <div class="card-body text-center cursor-pointer">
                                <i class="fas fa-user-md fa-3x text-success mb-3"></i>
                                <h5 class="card-title text-success">Médecin</h5>
                                <p class="card-text text-muted">Créer un compte pour un nouveau médecin</p>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="type_compte" value="medecin" id="type_medecin">
                                    <label class="form-check-label" for="type_medecin">
                                        Sélectionner
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <div class="card border-info h-100" onclick="selectionnerType('secretaire')">
                            <div class="card-body text-center cursor-pointer">
                                <i class="fas fa-user-tie fa-3x text-info mb-3"></i>
                                <h5 class="card-title text-info">Secrétaire</h5>
                                <p class="card-text text-muted">Créer un compte pour une nouvelle secrétaire</p>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="type_compte" value="secretaire" id="type_secretaire">
                                    <label class="form-check-label" for="type_secretaire">
                                        Sélectionner
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Formulaire de création -->
        <div class="card border-0 shadow-lg">
            <div class="card-header bg-gradient-success text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-form me-2"></i>Informations du nouveau compte
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" class="needs-validation" novalidate>
                    <input type="hidden" name="type_compte" id="type_compte_hidden">
                    
                    <!-- Informations de base -->
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-user me-2"></i>Informations personnelles
                            </h6>
                            
                            <div class="mb-3">
                                <label for="prenom" class="form-label">Prénom *</label>
                                <input type="text" class="form-control" id="prenom" name="prenom" required>
                                <div class="invalid-feedback">Veuillez saisir le prénom.</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="nom" class="form-label">Nom *</label>
                                <input type="text" class="form-control" id="nom" name="nom" required>
                                <div class="invalid-feedback">Veuillez saisir le nom.</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="email" class="form-label">Email professionnel *</label>
                                <input type="email" class="form-control" id="email" name="email" required>
                                <div class="invalid-feedback">Veuillez saisir un email valide.</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="telephone" class="form-label">Téléphone</label>
                                <input type="tel" class="form-control" id="telephone" name="telephone">
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <!-- Champs spécifiques médecin -->
                            <div id="champs_medecin" style="display: none;">
                                <h6 class="text-success mb-3">
                                    <i class="fas fa-stethoscope me-2"></i>Informations médicales
                                </h6>
                                
                                <div class="mb-3">
                                    <label for="specialite" class="form-label">Spécialité *</label>
                                    <select class="form-select" id="specialite" name="specialite">
                                        <option value="">Choisir une spécialité</option>
                                        <option value="Médecine générale">Médecine générale</option>
                                        <option value="Cardiologie">Cardiologie</option>
                                        <option value="Dermatologie">Dermatologie</option>
                                        <option value="Pédiatrie">Pédiatrie</option>
                                        <option value="Gynécologie">Gynécologie</option>
                                        <option value="Orthopédie">Orthopédie</option>
                                        <option value="Ophtalmologie">Ophtalmologie</option>
                                        <option value="ORL">ORL</option>
                                        <option value="Psychiatrie">Psychiatrie</option>
                                        <option value="Radiologie">Radiologie</option>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="tarif_consultation" class="form-label">Tarif consultation (€)</label>
                                    <input type="number" class="form-control" id="tarif_consultation" name="tarif_consultation" 
                                           step="0.01" min="0" placeholder="25.00">
                                </div>
                            </div>
                            
                            <!-- Champs spécifiques secrétaire -->
                            <div id="champs_secretaire" style="display: none;">
                                <h6 class="text-info mb-3">
                                    <i class="fas fa-briefcase me-2"></i>Informations professionnelles
                                </h6>
                                
                                <div class="mb-3">
                                    <label for="poste" class="form-label">Poste *</label>
                                    <select class="form-select" id="poste" name="poste">
                                        <option value="">Choisir un poste</option>
                                        <option value="Secrétaire Médicale">Secrétaire Médicale</option>
                                        <option value="Secrétaire Administrative">Secrétaire Administrative</option>
                                        <option value="Assistante de Direction">Assistante de Direction</option>
                                        <option value="Réceptionniste">Réceptionniste</option>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="service" class="form-label">Service</label>
                                    <select class="form-select" id="service" name="service">
                                        <option value="">Choisir un service</option>
                                        <option value="Accueil">Accueil</option>
                                        <option value="Administration">Administration</option>
                                        <option value="Comptabilité">Comptabilité</option>
                                        <option value="Planning">Planning</option>
                                        <option value="Archives">Archives</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Informations sur l'envoi -->
                    <div class="alert alert-info" role="alert">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Processus de création :</strong>
                        <ul class="mb-0 mt-2">
                            <li><strong>Médecin :</strong> Username et mot de passe temporaire générés automatiquement et envoyés par email</li>
                            <li><strong>Secrétaire :</strong> Invitation envoyée par email avec lien d'inscription sécurisé</li>
                            <li>L'utilisateur devra changer son mot de passe lors de la première connexion</li>
                        </ul>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="/admin/gestion-personnel" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i>Annuler
                        </a>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-paper-plane me-2"></i>Créer le compte et envoyer
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function selectionnerType(type) {
    // Mettre à jour les radio buttons
    document.getElementById('type_medecin').checked = (type === 'medecin');
    document.getElementById('type_secretaire').checked = (type === 'secretaire');
    document.getElementById('type_compte_hidden').value = type;
    
    // Afficher/masquer les champs spécifiques
    document.getElementById('champs_medecin').style.display = (type === 'medecin') ? 'block' : 'none';
    document.getElementById('champs_secretaire').style.display = (type === 'secretaire') ? 'block' : 'none';
    
    // Mettre à jour les validations requises
    if (type === 'medecin') {
        document.getElementById('specialite').required = true;
        document.getElementById('poste').required = false;
    } else if (type === 'secretaire') {
        document.getElementById('specialite').required = false;
        document.getElementById('poste').required = true;
    }
}

// Validation Bootstrap
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// Style pour les cartes cliquables
document.addEventListener('DOMContentLoaded', function() {
    const cards = document.querySelectorAll('.cursor-pointer');
    cards.forEach(card => {
        card.style.cursor = 'pointer';
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.02)';
            this.style.transition = 'transform 0.2s';
        });
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1)';
        });
    });
});
</script>

<style>
.cursor-pointer {
    cursor: pointer;
    transition: transform 0.2s;
}
.cursor-pointer:hover {
    transform: scale(1.02);
}
</style>
{% endblock %}
