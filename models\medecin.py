﻿from flask_sqlalchemy import SQLAlchemy
from datetime import datetime

# Import de l'instance db depuis database
from database import db

class Medecin(db.Model):
    __tablename__ = 'medecins'
    
    id = db.Column(db.Integer, primary_key=True)
    nom = db.Column(db.String(100), nullable=False)
    prenom = db.Column(db.String(100), nullable=False)
    specialite = db.Column(db.String(100), nullable=False)
    telephone = db.Column(db.String(20))
    email = db.Column(db.String(100), unique=True)
    adresse = db.Column(db.Text)
    
    # Informations professionnelles
    numero_rpps = db.Column(db.String(20))  # Numéro RPPS
    numero_adeli = db.Column(db.String(20))  # Numéro ADELI
    secteur_conventionnement = db.Column(db.Enum('secteur_1', 'secteur_2', 'non_conventionne'))
    tarif_consultation = db.Column(db.Float)
    
    # Horaires de travail
    horaires_lundi = db.Column(db.String(50))
    horaires_mardi = db.Column(db.String(50))
    horaires_mercredi = db.Column(db.String(50))
    horaires_jeudi = db.Column(db.String(50))
    horaires_vendredi = db.Column(db.String(50))
    horaires_samedi = db.Column(db.String(50))
    horaires_dimanche = db.Column(db.String(50))
    
    # Métadonnées
    date_creation = db.Column(db.DateTime, default=datetime.utcnow)
    actif = db.Column(db.Boolean, default=True)
    
    # Relations
    rendez_vous = db.relationship('RendezVous', backref='medecin', lazy=True, cascade='all, delete-orphan')
    consultations = db.relationship('Consultation', backref='medecin', lazy=True)
    
    def __repr__(self):
        return f'<Medecin Dr. {self.prenom} {self.nom}>'
    
    @property
    def nom_complet(self):
        return f"Dr. {self.prenom} {self.nom}"
    
    @property
    def prochains_rdv_aujourd_hui(self):
        from datetime import date
        from models.rendez_vous import RendezVous
        return RendezVous.query.filter_by(
            medecin_id=self.id
        ).filter(
            db.func.date(RendezVous.date_rdv) == date.today(),
            RendezVous.statut.in_(['planifie', 'confirme'])
        ).order_by(RendezVous.date_rdv).all()
    
    def to_dict(self):
        return {
            'id': self.id,
            'nom': self.nom,
            'prenom': self.prenom,
            'nom_complet': self.nom_complet,
            'specialite': self.specialite,
            'telephone': self.telephone,
            'email': self.email,
            'adresse': self.adresse,
            'tarif_consultation': self.tarif_consultation,
            'secteur_conventionnement': self.secteur_conventionnement
        }
