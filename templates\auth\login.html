{% extends "auth/base_auth.html" %}

{% block title %}Connexion - Ilaji Medical{% endblock %}

{% block auth_title %}
    {% if role %}
        Connexion {{ role.title() }}
    {% else %}
        Connexion
    {% endif %}
{% endblock %}
{% block auth_subtitle %}
    {% if role %}
        Acc<PERSON>dez à votre espace {{ role }}
    {% else %}
        Accédez à votre espace personnel
    {% endif %}
{% endblock %}

{% block auth_content %}
{% if role %}
<div class="role-indicator mb-4">
    <div class="d-flex align-items-center justify-content-center p-3 rounded-3"
         style="background: linear-gradient(135deg,
         {% if role == 'admin' %}#ef4444{% elif role == 'medecin' %}#10b981{% elif role == 'secretaire' %}#f59e0b{% else %}#06b6d4{% endif %} 0%,
         {% if role == 'admin' %}#dc2626{% elif role == 'medecin' %}#059669{% elif role == 'secretaire' %}#d97706{% else %}#0891b2{% endif %} 100%); color: white;">
        <i class="fas fa-{% if role == 'admin' %}user-shield{% elif role == 'medecin' %}user-md{% elif role == 'secretaire' %}user-tie{% else %}user{% endif %} fa-2x me-3"></i>
        <div>
            <h5 class="mb-0">Connexion {{ role.title() }}</h5>
            <small class="opacity-75">
                {% if role == 'admin' %}Administrateur système
                {% elif role == 'medecin' %}Professionnel de santé
                {% elif role == 'secretaire' %}Personnel administratif
                {% else %}Espace patient{% endif %}
            </small>
        </div>
    </div>
    <div class="text-center mt-3">
        <a href="{{ url_for('auth.welcome') }}" class="btn btn-outline-secondary btn-sm">
            <i class="fas fa-arrow-left me-1"></i>Changer de rôle
        </a>
    </div>
</div>
{% endif %}

<form method="POST" novalidate>
    {{ form.hidden_tag() }}
    {% if role %}
        <input type="hidden" name="role" value="{{ role }}">
    {% endif %}
    
    <div class="form-floating">
        {{ form.email(class="form-control" + (" is-invalid" if form.email.errors else ""), placeholder="Email") }}
        {{ form.email.label(class="form-label") }}
        {% if form.email.errors %}
            <div class="invalid-feedback">
                {% for error in form.email.errors %}
                    <small>{{ error }}</small>
                {% endfor %}
            </div>
        {% endif %}
    </div>
    
    <div class="form-floating">
        {{ form.password(class="form-control" + (" is-invalid" if form.password.errors else ""), placeholder="Mot de passe") }}
        {{ form.password.label(class="form-label") }}
        {% if form.password.errors %}
            <div class="invalid-feedback">
                {% for error in form.password.errors %}
                    <small>{{ error }}</small>
                {% endfor %}
            </div>
        {% endif %}
    </div>
    
    <div class="d-flex justify-content-between align-items-center">
        <div class="form-check">
            {{ form.remember_me(class="form-check-input") }}
            {{ form.remember_me.label(class="form-check-label") }}
        </div>
        <a href="{{ url_for('auth.forgot_password') }}" class="auth-link">
            <small>Mot de passe oublié ?</small>
        </a>
    </div>
    
    {{ form.submit(class="btn btn-primary btn-auth") }}
</form>

<div class="divider">
    <span>ou</span>
</div>

<div class="row g-2">
    <div class="col-6">
        <a href="#" class="social-btn w-100">
            <i class="fab fa-google"></i>
            Google
        </a>
    </div>
    <div class="col-6">
        <a href="#" class="social-btn w-100">
            <i class="fab fa-microsoft"></i>
            Microsoft
        </a>
    </div>
</div>

<div class="text-center mt-4">
    <p class="text-muted">
        Pas encore de compte ? 
        <a href="{{ url_for('auth.register') }}" class="auth-link">Créer un compte</a>
    </p>
</div>
{% endblock %}

{% block auth_scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Animation d'entrée pour les champs
    const formFields = document.querySelectorAll('.form-floating');
    formFields.forEach((field, index) => {
        field.style.opacity = '0';
        field.style.transform = 'translateY(20px)';
        setTimeout(() => {
            field.style.transition = 'all 0.5s ease';
            field.style.opacity = '1';
            field.style.transform = 'translateY(0)';
        }, index * 100);
    });
    
    // Validation en temps réel
    const emailField = document.getElementById('email');
    const passwordField = document.getElementById('password');
    
    if (emailField) {
        emailField.addEventListener('blur', function() {
            validateEmail(this);
        });
    }
    
    if (passwordField) {
        passwordField.addEventListener('input', function() {
            validatePassword(this);
        });
    }
    
    function validateEmail(field) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        const isValid = emailRegex.test(field.value);
        
        field.classList.remove('is-valid', 'is-invalid');
        if (field.value && isValid) {
            field.classList.add('is-valid');
        } else if (field.value && !isValid) {
            field.classList.add('is-invalid');
        }
    }
    
    function validatePassword(field) {
        const isValid = field.value.length >= 8;
        
        field.classList.remove('is-valid', 'is-invalid');
        if (field.value && isValid) {
            field.classList.add('is-valid');
        } else if (field.value && !isValid) {
            field.classList.add('is-invalid');
        }
    }
    
    // Effet de focus sur les champs
    document.querySelectorAll('.form-control').forEach(field => {
        field.addEventListener('focus', function() {
            this.parentElement.style.transform = 'scale(1.02)';
        });
        
        field.addEventListener('blur', function() {
            this.parentElement.style.transform = 'scale(1)';
        });
    });
    
    // Animation du bouton de soumission
    const submitBtn = document.querySelector('.btn-auth');
    if (submitBtn) {
        submitBtn.addEventListener('click', function(e) {
            if (this.form.checkValidity()) {
                this.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Connexion...';
                this.disabled = true;
            }
        });
    }
});
</script>
{% endblock %}
