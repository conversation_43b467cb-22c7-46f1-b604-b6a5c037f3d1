{% extends "base.html" %}

{% block title %}Gestion des Utilisateurs - Ilaji Medical{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="display-6 text-primary mb-1">
                    <i class="fas fa-users me-3"></i>Gestion des Utilisateurs
                </h1>
                <p class="text-muted mb-0">Administration complète des utilisateurs du système</p>
            </div>
            <div>
                <a href="/admin/creer-utilisateur" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>Nouvel Utilisateur
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Statistiques rapides -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <i class="fas fa-user-md fa-2x text-success mb-2"></i>
                <h4 class="text-success">{{ utilisateurs|selectattr("type", "equalto", "Médecin")|list|length }}</h4>
                <p class="text-muted mb-0">Médecins</p>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <i class="fas fa-user fa-2x text-info mb-2"></i>
                <h4 class="text-info">{{ utilisateurs|selectattr("type", "equalto", "Patient")|list|length }}</h4>
                <p class="text-muted mb-0">Patients</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <i class="fas fa-user-shield fa-2x text-danger mb-2"></i>
                <h4 class="text-danger">{{ utilisateurs|selectattr("type", "equalto", "Administrateur")|list|length }}</h4>
                <p class="text-muted mb-0">Administrateurs</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <i class="fas fa-users fa-2x text-primary mb-2"></i>
                <h4 class="text-primary">{{ utilisateurs|length }}</h4>
                <p class="text-muted mb-0">Total</p>
            </div>
        </div>
    </div>
</div>

<!-- Liste des utilisateurs -->
<div class="card border-0 shadow-sm">
    <div class="card-header bg-white">
        <h5 class="card-title mb-0">
            <i class="fas fa-list me-2"></i>Liste des Utilisateurs
        </h5>
    </div>
    <div class="card-body">
        {% if utilisateurs %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-light">
                    <tr>
                        <th>ID</th>
                        <th>Nom Complet</th>
                        <th>Type</th>
                        <th>Contact</th>
                        <th>Spécialité/CIN</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for utilisateur in utilisateurs %}
                    <tr>
                        <td><span class="badge bg-secondary">#{{ utilisateur.id }}</span></td>
                        <td>
                            <strong>{{ utilisateur.prenom }} {{ utilisateur.nom }}</strong>
                        </td>
                        <td>
                            {% if utilisateur.type == 'Médecin' %}
                                <span class="badge bg-success">{{ utilisateur.type }}</span>
                            {% elif utilisateur.type == 'Administrateur' %}
                                <span class="badge bg-danger">{{ utilisateur.type }}</span>
                            {% else %}
                                <span class="badge bg-info">{{ utilisateur.type }}</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="small">
                                <i class="fas fa-envelope me-1"></i>{{ utilisateur.email }}<br>
                                <i class="fas fa-phone me-1"></i>{{ utilisateur.telephone }}
                            </div>
                        </td>
                        <td>
                            {% if utilisateur.type == 'Médecin' %}
                                <span class="text-success">{{ utilisateur.specialite }}</span>
                            {% elif utilisateur.type == 'Administrateur' %}
                                <span class="text-danger">{{ utilisateur.role }}</span>
                            {% else %}
                                <span class="text-info">CIN: {{ utilisateur.cin }}</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="/admin/utilisateur/{{ utilisateur.id }}" class="btn btn-outline-primary" title="Voir détails">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="/admin/utilisateur/{{ utilisateur.id }}/modifier" class="btn btn-outline-warning" title="Modifier">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button class="btn btn-outline-danger" onclick="confirmerSuppression({{ utilisateur.id }}, '{{ utilisateur.prenom }} {{ utilisateur.nom }}')" title="Supprimer">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-users fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">Aucun utilisateur trouvé</h5>
            <p class="text-muted">Commencez par ajouter des médecins et des patients.</p>
            <a href="/admin/creer-utilisateur" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>Ajouter un utilisateur
            </a>
        </div>
        {% endif %}
    </div>
</div>

<script>
function confirmerSuppression(id, nom) {
    if (confirm(`Êtes-vous sûr de vouloir supprimer l'utilisateur "${nom}" ?`)) {
        // Redirection vers la route de suppression
        window.location.href = `/admin/utilisateur/${id}/supprimer`;
    }
}
</script>
{% endblock %}
