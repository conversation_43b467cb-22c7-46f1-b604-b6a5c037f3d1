<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Connexion {{ role.title() }} - <PERSON><PERSON><PERSON></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #2563eb;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, 
                {% if role == 'medecin' %}#10b981{% else %}#f59e0b{% endif %} 0%, 
                {% if role == 'medecin' %}#059669{% else %}#d97706{% endif %} 100%);
            min-height: 100vh;
            margin: 0;
            padding: 0;
        }
        
        .login-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem 1rem;
        }
        
        .login-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
            max-width: 450px;
            width: 100%;
        }
        
        .login-header {
            background: linear-gradient(135deg, 
                {% if role == 'medecin' %}var(--success-color){% else %}var(--warning-color){% endif %} 0%, 
                {% if role == 'medecin' %}#059669{% else %}#d97706{% endif %} 100%);
            color: white;
            padding: 2.5rem 2rem;
            text-align: center;
        }
        
        .role-icon {
            font-size: 3.5rem;
            margin-bottom: 1rem;
            display: block;
        }
        
        .login-header h1 {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            color: white;
        }
        
        .login-header p {
            font-size: 1rem;
            opacity: 0.9;
            margin: 0;
        }
        
        .login-body {
            padding: 2.5rem 2rem;
        }
        
        .form-floating {
            margin-bottom: 1.5rem;
        }
        
        .form-control {
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            padding: 0.75rem 1rem;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: {% if role == 'medecin' %}var(--success-color){% else %}var(--warning-color){% endif %};
            box-shadow: 0 0 0 0.2rem {% if role == 'medecin' %}rgba(16, 185, 129, 0.25){% else %}rgba(245, 158, 11, 0.25){% endif %};
        }
        
        .btn-login {
            background: linear-gradient(135deg, 
                {% if role == 'medecin' %}var(--success-color){% else %}var(--warning-color){% endif %} 0%, 
                {% if role == 'medecin' %}#059669{% else %}#d97706{% endif %} 100%);
            border: none;
            border-radius: 10px;
            color: white;
            padding: 0.75rem 2rem;
            font-weight: 600;
            font-size: 1rem;
            width: 100%;
            transition: all 0.3s ease;
            margin-top: 1rem;
        }
        
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px -5px {% if role == 'medecin' %}rgba(16, 185, 129, 0.4){% else %}rgba(245, 158, 11, 0.4){% endif %};
            color: white;
        }
        
        .back-link {
            text-align: center;
            margin-top: 2rem;
        }
        
        .back-link a {
            color: #6b7280;
            text-decoration: none;
            font-size: 0.9rem;
            transition: color 0.3s ease;
        }
        
        .back-link a:hover {
            color: #374151;
        }
        
        .alert {
            border-radius: 10px;
            border: none;
            margin-bottom: 1.5rem;
        }
        
        .professional-badge {
            background: {% if role == 'medecin' %}var(--success-color){% else %}var(--warning-color){% endif %};
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            display: inline-block;
            margin-bottom: 1.5rem;
        }
        
        .security-note {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 10px;
            padding: 1rem;
            margin-top: 1.5rem;
            font-size: 0.9rem;
            color: #6b7280;
        }
        
        @media (max-width: 768px) {
            .login-header h1 {
                font-size: 1.5rem;
            }
            
            .login-header,
            .login-body {
                padding: 2rem 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <i class="fas fa-{% if role == 'medecin' %}user-md{% else %}user-tie{% endif %} role-icon"></i>
                <h1>Connexion {{ role.title() }}</h1>
                <p>Accès professionnel sécurisé</p>
            </div>
            
            <div class="login-body">
                <div class="text-center">
                    <span class="professional-badge">
                        <i class="fas fa-shield-alt me-1"></i>
                        {% if role == 'medecin' %}Professionnel de Santé{% else %}Personnel Administratif{% endif %}
                    </span>
                </div>
                
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ 'danger' if category == 'error' else 'success' }}">
                                <i class="fas fa-{{ 'exclamation-triangle' if category == 'error' else 'check-circle' }} me-2"></i>
                                {{ message }}
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}
                
                <form method="POST" action="/auth/staff-login">
                    <input type="hidden" name="role" value="{{ role }}">
                    
                    <div class="form-floating">
                        <input type="text" class="form-control" id="username" name="username" placeholder="Nom d'utilisateur" required>
                        <label for="username">
                            <i class="fas fa-user me-2"></i>Nom d'utilisateur
                        </label>
                    </div>
                    
                    <div class="form-floating">
                        <input type="password" class="form-control" id="password" name="password" placeholder="Mot de passe" required>
                        <label for="password">
                            <i class="fas fa-lock me-2"></i>Mot de passe
                        </label>
                    </div>
                    
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="remember" name="remember">
                        <label class="form-check-label" for="remember">
                            Se souvenir de moi
                        </label>
                    </div>
                    
                    <button type="submit" class="btn btn-login">
                        <i class="fas fa-sign-in-alt me-2"></i>Se connecter
                    </button>
                </form>
                
                <div class="security-note">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Note de sécurité :</strong> Vos identifiants sont fournis par l'administration. 
                    En cas d'oubli, contactez votre administrateur système.
                </div>
                
                <div class="back-link">
                    <a href="/auth/role-choice?role={{ role }}">
                        <i class="fas fa-arrow-left me-1"></i>Retour aux options
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Animation d'entrée
        document.addEventListener('DOMContentLoaded', function() {
            const card = document.querySelector('.login-card');
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';
            
            setTimeout(() => {
                card.style.transition = 'all 0.6s ease';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, 100);
        });
    </script>
</body>
</html>
