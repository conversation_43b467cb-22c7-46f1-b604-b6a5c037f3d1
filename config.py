﻿import os
from dotenv import load_dotenv

load_dotenv()

class Config:
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'ilaji-secret-key-change-in-production'
    
    # Configuration SQLite pour les tests (plus simple que MySQL)
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///ilaji_medical.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # Configuration Flask
    DEBUG = os.environ.get('FLASK_DEBUG') or True
    
    # Configuration pour les uploads
    UPLOAD_FOLDER = 'static/uploads'
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB max file size
