{% extends "base.html" %}

{% block title %}Rendez-vous - <PERSON><PERSON><PERSON> Medical{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="display-6 text-primary mb-1">
                    <i class="fas fa-calendar-alt me-3"></i>Rendez-vous
                </h1>
                <p class="text-muted mb-0">Gestion des rendez-vous du cabinet</p>
            </div>
            <div>
                <a href="{{ url_for('nouveau_rendez_vous') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>Nouveau RDV
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-gradient-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>Tous les rendez-vous ({{ rendez_vous|length }})
                </h5>
            </div>
            <div class="card-body">
                {% if rendez_vous %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Date & Heure</th>
                                    <th>Patient</th>
                                    <th>Médecin</th>
                                    <th>Motif</th>
                                    <th>Statut</th>
                                    <th>Priorité</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for rdv in rendez_vous %}
                                <tr class="{{ 'table-warning' if rdv.priorite == 'urgente' else 'table-danger' if rdv.priorite == 'tres_urgente' else '' }}">
                                    <td>
                                        <strong>{{ rdv.date_formatee }}</strong>
                                        <br>
                                        <small class="text-muted">{{ rdv.duree }} min</small>
                                    </td>
                                    <td>
                                        {% if rdv.patient %}
                                            <strong>{{ rdv.patient.nom_complet }}</strong>
                                            <br>
                                            <small class="text-muted">{{ rdv.patient.telephone or 'Pas de téléphone' }}</small>
                                        {% else %}
                                            <span class="text-muted">Patient supprimé</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if rdv.medecin %}
                                            {{ rdv.medecin.nom_complet }}
                                            <br>
                                            <small class="text-muted">{{ rdv.medecin.specialite }}</small>
                                        {% else %}
                                            <span class="text-muted">Médecin supprimé</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ rdv.motif or '-' }}</td>
                                    <td>
                                        {% if rdv.statut == 'planifie' %}
                                            <span class="badge bg-primary">Planifié</span>
                                        {% elif rdv.statut == 'confirme' %}
                                            <span class="badge bg-success">Confirmé</span>
                                        {% elif rdv.statut == 'en_cours' %}
                                            <span class="badge bg-info">En cours</span>
                                        {% elif rdv.statut == 'termine' %}
                                            <span class="badge bg-secondary">Terminé</span>
                                        {% elif rdv.statut == 'annule' %}
                                            <span class="badge bg-danger">Annulé</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if rdv.priorite == 'normale' %}
                                            <span class="badge bg-light text-dark">Normale</span>
                                        {% elif rdv.priorite == 'urgente' %}
                                            <span class="badge bg-warning">Urgente</span>
                                        {% elif rdv.priorite == 'tres_urgente' %}
                                            <span class="badge bg-danger">Très urgente</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary" title="Voir détails">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-outline-warning" title="Modifier">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-outline-danger" title="Annuler">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-calendar-times fa-4x text-muted mb-3"></i>
                        <h4 class="text-muted">Aucun rendez-vous programmé</h4>
                        <p class="text-muted">Commencez par créer votre premier rendez-vous</p>
                        <a href="{{ url_for('nouveau_rendez_vous') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Créer un rendez-vous
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
