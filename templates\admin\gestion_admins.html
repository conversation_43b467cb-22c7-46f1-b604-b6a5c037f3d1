{% extends "base.html" %}

{% block title %}Gestion des Administrateurs - Ilaji Medical{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="display-6 text-danger mb-1">
                    <i class="fas fa-user-shield me-3"></i>Gestion des Administrateurs
                </h1>
                <p class="text-muted mb-0">Administration des comptes administrateurs (Super Admin uniquement)</p>
            </div>
            <div>
                <button class="btn btn-danger" onclick="alert('Création d\'admin en cours de développement')">
                    <i class="fas fa-plus me-2"></i>Nouvel Admin
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Hiérarchie des admins -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-gradient-danger text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-sitemap me-2"></i>Hiérarchie Administrative
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 text-center">
                        <div class="bg-danger bg-gradient rounded-circle p-3 mx-auto mb-3" style="width: 80px; height: 80px;">
                            <i class="fas fa-crown fa-2x text-white"></i>
                        </div>
                        <h6 class="text-danger">Niveau 3</h6>
                        <p class="fw-bold">Super Administrateur</p>
                        <small class="text-muted">Gère tous les admins et le système complet</small>
                    </div>
                    <div class="col-md-4 text-center">
                        <div class="bg-warning bg-gradient rounded-circle p-3 mx-auto mb-3" style="width: 80px; height: 80px;">
                            <i class="fas fa-cogs fa-2x text-white"></i>
                        </div>
                        <h6 class="text-warning">Niveau 2</h6>
                        <p class="fw-bold">Admin Système Médical</p>
                        <small class="text-muted">Gère le système médical et les paramètres</small>
                    </div>
                    <div class="col-md-4 text-center">
                        <div class="bg-primary bg-gradient rounded-circle p-3 mx-auto mb-3" style="width: 80px; height: 80px;">
                            <i class="fas fa-users fa-2x text-white"></i>
                        </div>
                        <h6 class="text-primary">Niveau 1</h6>
                        <p class="fw-bold">Admin Principal</p>
                        <small class="text-muted">Gère les médecins et secrétaires</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Liste des administrateurs -->
<div class="card border-0 shadow-sm">
    <div class="card-header bg-white">
        <h5 class="card-title mb-0">
            <i class="fas fa-list me-2"></i>Administrateurs du système ({{ admins|length }})
        </h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-light">
                    <tr>
                        <th>Administrateur</th>
                        <th>Rôle & Niveau</th>
                        <th>Contact</th>
                        <th>Permissions</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for admin in admins %}
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="bg-danger bg-gradient rounded-circle p-2 me-3">
                                    {% if admin.niveau == 3 %}
                                        <i class="fas fa-crown text-white"></i>
                                    {% elif admin.niveau == 2 %}
                                        <i class="fas fa-cogs text-white"></i>
                                    {% else %}
                                        <i class="fas fa-user-shield text-white"></i>
                                    {% endif %}
                                </div>
                                <div>
                                    <strong>{{ admin.prenom }} {{ admin.nom }}</strong><br>
                                    <small class="text-muted">{{ admin.username }}</small>
                                </div>
                            </div>
                        </td>
                        <td>
                            {% if admin.niveau == 3 %}
                                <span class="badge bg-danger">{{ admin.role }}</span>
                            {% elif admin.niveau == 2 %}
                                <span class="badge bg-warning">{{ admin.role }}</span>
                            {% else %}
                                <span class="badge bg-primary">{{ admin.role }}</span>
                            {% endif %}
                            <br>
                            <small class="text-muted">Niveau {{ admin.niveau }}</small>
                        </td>
                        <td>
                            <div class="small">
                                <i class="fas fa-envelope me-1"></i>{{ admin.email }}<br>
                                <i class="fas fa-phone me-1"></i>{{ admin.telephone }}
                            </div>
                        </td>
                        <td>
                            <div class="small">
                                {% for permission in admin.permissions[:2] %}
                                    <span class="badge bg-secondary me-1">{{ permission.replace('_', ' ').title() }}</span>
                                {% endfor %}
                                {% if admin.permissions|length > 2 %}
                                    <br><small class="text-muted">+{{ admin.permissions|length - 2 }} autres</small>
                                {% endif %}
                            </div>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="/admin/utilisateur/{{ admin.id }}" class="btn btn-outline-primary" title="Voir détails">
                                    <i class="fas fa-eye"></i>
                                </a>
                                {% if admin.niveau < 3 %}
                                <a href="/admin/reset-password/{{ admin.id }}" class="btn btn-outline-warning" title="Reset password">
                                    <i class="fas fa-key"></i>
                                </a>
                                <button class="btn btn-outline-danger" onclick="confirmerSuppression({{ admin.id }}, '{{ admin.prenom }} {{ admin.nom }}')" title="Supprimer">
                                    <i class="fas fa-trash"></i>
                                </button>
                                {% else %}
                                <button class="btn btn-outline-secondary" disabled title="Super Admin protégé">
                                    <i class="fas fa-shield-alt"></i>
                                </button>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Permissions détaillées -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-key me-2"></i>Matrice des permissions
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Permission</th>
                                <th class="text-center">Admin Principal</th>
                                <th class="text-center">Admin Système</th>
                                <th class="text-center">Super Admin</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Gérer médecins</td>
                                <td class="text-center"><i class="fas fa-check text-success"></i></td>
                                <td class="text-center"><i class="fas fa-times text-danger"></i></td>
                                <td class="text-center"><i class="fas fa-check text-success"></i></td>
                            </tr>
                            <tr>
                                <td>Gérer secrétaires</td>
                                <td class="text-center"><i class="fas fa-check text-success"></i></td>
                                <td class="text-center"><i class="fas fa-times text-danger"></i></td>
                                <td class="text-center"><i class="fas fa-check text-success"></i></td>
                            </tr>
                            <tr>
                                <td>Créer comptes personnel</td>
                                <td class="text-center"><i class="fas fa-check text-success"></i></td>
                                <td class="text-center"><i class="fas fa-times text-danger"></i></td>
                                <td class="text-center"><i class="fas fa-check text-success"></i></td>
                            </tr>
                            <tr>
                                <td>Gérer système médical</td>
                                <td class="text-center"><i class="fas fa-times text-danger"></i></td>
                                <td class="text-center"><i class="fas fa-check text-success"></i></td>
                                <td class="text-center"><i class="fas fa-check text-success"></i></td>
                            </tr>
                            <tr>
                                <td>Paramètres médicaux</td>
                                <td class="text-center"><i class="fas fa-times text-danger"></i></td>
                                <td class="text-center"><i class="fas fa-check text-success"></i></td>
                                <td class="text-center"><i class="fas fa-check text-success"></i></td>
                            </tr>
                            <tr>
                                <td>Créer/Gérer admins</td>
                                <td class="text-center"><i class="fas fa-times text-danger"></i></td>
                                <td class="text-center"><i class="fas fa-times text-danger"></i></td>
                                <td class="text-center"><i class="fas fa-check text-success"></i></td>
                            </tr>
                            <tr>
                                <td>Accès système complet</td>
                                <td class="text-center"><i class="fas fa-times text-danger"></i></td>
                                <td class="text-center"><i class="fas fa-times text-danger"></i></td>
                                <td class="text-center"><i class="fas fa-check text-success"></i></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function confirmerSuppression(id, nom) {
    if (confirm(`Êtes-vous sûr de vouloir supprimer l'administrateur "${nom}" ?\n\nCette action est irréversible et peut affecter l'accès au système.`)) {
        window.location.href = `/admin/utilisateur/${id}/supprimer`;
    }
}
</script>
{% endblock %}
