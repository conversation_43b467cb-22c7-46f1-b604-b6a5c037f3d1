{% extends "base.html" %}

{% block title %}Changer le mot de passe - <PERSON><PERSON><PERSON>{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="display-6 text-primary mb-1">
                    <i class="fas fa-key me-3"></i>Changer le mot de passe
                </h1>
                <p class="text-muted mb-0">Modifiez votre mot de passe pour sécuriser votre compte</p>
            </div>
            <div>
                <a href="{{ url_for('auth.profile') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Retour au profil
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-6 mx-auto">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-gradient-warning text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-shield-alt me-2"></i>Sécurité du compte
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" novalidate>
                    {{ form.hidden_tag() }}
                    
                    <div class="mb-3">
                        {{ form.current_password.label(class="form-label") }}
                        {{ form.current_password(class="form-control" + (" is-invalid" if form.current_password.errors else "")) }}
                        {% if form.current_password.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.current_password.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.new_password.label(class="form-label") }}
                        {{ form.new_password(class="form-control" + (" is-invalid" if form.new_password.errors else "")) }}
                        <div class="password-strength" id="passwordStrength"></div>
                        {% if form.new_password.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.new_password.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.new_password_confirm.label(class="form-label") }}
                        {{ form.new_password_confirm(class="form-control" + (" is-invalid" if form.new_password_confirm.errors else "")) }}
                        {% if form.new_password_confirm.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.new_password_confirm.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="d-flex justify-content-end gap-2">
                        <a href="{{ url_for('auth.profile') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>Annuler
                        </a>
                        {{ form.submit(class="btn btn-warning") }}
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Conseils de sécurité -->
        <div class="card border-0 shadow-sm mt-4">
            <div class="card-header bg-gradient-info text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-lightbulb me-2"></i>Conseils de sécurité
                </h5>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        Utilisez au moins 8 caractères
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        Mélangez majuscules et minuscules
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        Incluez des chiffres et des symboles
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        Évitez les mots du dictionnaire
                    </li>
                    <li class="mb-0">
                        <i class="fas fa-check text-success me-2"></i>
                        Ne réutilisez pas d'anciens mots de passe
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<style>
.password-strength {
    margin-top: 0.5rem;
    font-size: 0.75rem;
}

.strength-bar {
    height: 4px;
    border-radius: 2px;
    margin-top: 0.25rem;
    transition: all 0.3s ease;
}

.strength-weak { background-color: #ef4444; width: 25%; }
.strength-fair { background-color: #f59e0b; width: 50%; }
.strength-good { background-color: #10b981; width: 75%; }
.strength-strong { background-color: #059669; width: 100%; }
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const newPasswordField = document.getElementById('new_password');
    const confirmPasswordField = document.getElementById('new_password_confirm');
    const strengthIndicator = document.getElementById('passwordStrength');
    
    if (newPasswordField) {
        newPasswordField.addEventListener('input', function() {
            checkPasswordStrength(this.value);
            validatePasswordMatch();
        });
    }
    
    if (confirmPasswordField) {
        confirmPasswordField.addEventListener('input', validatePasswordMatch);
    }
    
    function checkPasswordStrength(password) {
        let strength = 0;
        let feedback = '';
        
        if (password.length >= 8) strength++;
        if (/[a-z]/.test(password)) strength++;
        if (/[A-Z]/.test(password)) strength++;
        if (/[0-9]/.test(password)) strength++;
        if (/[^A-Za-z0-9]/.test(password)) strength++;
        
        const strengthBar = document.createElement('div');
        strengthBar.className = 'strength-bar';
        
        switch (strength) {
            case 0:
            case 1:
                strengthBar.className += ' strength-weak';
                feedback = 'Mot de passe faible';
                break;
            case 2:
                strengthBar.className += ' strength-fair';
                feedback = 'Mot de passe moyen';
                break;
            case 3:
            case 4:
                strengthBar.className += ' strength-good';
                feedback = 'Mot de passe bon';
                break;
            case 5:
                strengthBar.className += ' strength-strong';
                feedback = 'Mot de passe fort';
                break;
        }
        
        strengthIndicator.innerHTML = `
            <div style="color: ${strength <= 1 ? '#ef4444' : strength <= 2 ? '#f59e0b' : '#10b981'};">
                ${feedback}
            </div>
            ${strengthBar.outerHTML}
        `;
    }
    
    function validatePasswordMatch() {
        const newPassword = newPasswordField.value;
        const confirmPassword = confirmPasswordField.value;
        
        confirmPasswordField.classList.remove('is-valid', 'is-invalid');
        
        if (confirmPassword && newPassword !== confirmPassword) {
            confirmPasswordField.classList.add('is-invalid');
        } else if (confirmPassword && newPassword === confirmPassword) {
            confirmPasswordField.classList.add('is-valid');
        }
    }
    
    // Animation du bouton de soumission
    const submitBtn = document.querySelector('button[type="submit"]');
    if (submitBtn) {
        submitBtn.addEventListener('click', function(e) {
            if (this.form.checkValidity()) {
                this.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Changement...';
                this.disabled = true;
            }
        });
    }
});
</script>
{% endblock %}
