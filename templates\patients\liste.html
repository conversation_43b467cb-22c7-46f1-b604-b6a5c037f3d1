﻿{% extends "base.html" %}

{% block title %}Patients - Ilaji Medical{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="display-6 text-primary mb-1">
                    <i class="fas fa-users me-3"></i>Gestion des Patients
                </h1>
                <p class="text-muted mb-0">Gérez vos patients et leurs dossiers médicaux</p>
            </div>
            <div>
                <a href="{{ url_for('nouveau_patient') }}" class="btn btn-primary btn-lg">
                    <i class="fas fa-user-plus me-2"></i>Nouveau Patient
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Barre de recherche et filtres -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-6">
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="fas fa-search"></i>
                            </span>
                            <input type="text" class="form-control search-input" name="search" 
                                   value="{{ search or '' }}" placeholder="Rechercher un patient (nom, prénom, téléphone...)">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" name="sexe">
                            <option value="">Tous les sexes</option>
                            <option value="M">Hommes</option>
                            <option value="F">Femmes</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <div class="d-grid gap-2 d-md-flex">
                            <button type="submit" class="btn btn-outline-primary">
                                <i class="fas fa-filter me-1"></i>Filtrer
                            </button>
                            <a href="{{ url_for('patients') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-1"></i>Reset
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Liste des patients -->
<div class="row">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-gradient-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>Liste des Patients
                    <span class="badge bg-light text-primary ms-2">{{ patients|length }}</span>
                </h5>
            </div>
            <div class="card-body p-0">
                {% if patients %}
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th class="ps-4">Patient</th>
                                    <th>Informations</th>
                                    <th>Contact</th>
                                    <th>Dernière visite</th>
                                    <th>Prochaine visite</th>
                                    <th class="text-center">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for patient in patients %}
                                <tr class="patient-row" data-patient-id="{{ patient.id }}">
                                    <td class="ps-4">
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-circle me-3">
                                                <i class="fas fa-user"></i>
                                            </div>
                                            <div>
                                                <h6 class="mb-1 fw-bold">{{ patient.nom_complet }}</h6>
                                                <small class="text-muted">
                                                    {% if patient.numero_securite_sociale %}
                                                        SS: {{ patient.numero_securite_sociale }}
                                                    {% else %}
                                                        Pas de n° SS
                                                    {% endif %}
                                                </small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            <span class="badge bg-{{ 'primary' if patient.sexe == 'M' else 'pink' }} mb-1">
                                                {{ 'Homme' if patient.sexe == 'M' else 'Femme' }}
                                            </span>
                                        </div>
                                        <div class="text-muted small">
                                            <i class="fas fa-birthday-cake me-1"></i>
                                            {{ patient.date_naissance.strftime('%d/%m/%Y') }} ({{ patient.age }} ans)
                                        </div>
                                        {% if patient.profession %}
                                        <div class="text-muted small">
                                            <i class="fas fa-briefcase me-1"></i>{{ patient.profession }}
                                        </div>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if patient.telephone %}
                                        <div class="mb-1">
                                            <i class="fas fa-phone me-1 text-success"></i>
                                            <a href="tel:{{ patient.telephone }}" class="text-decoration-none">
                                                {{ patient.telephone }}
                                            </a>
                                        </div>
                                        {% endif %}
                                        {% if patient.email %}
                                        <div>
                                            <i class="fas fa-envelope me-1 text-info"></i>
                                            <a href="mailto:{{ patient.email }}" class="text-decoration-none small">
                                                {{ patient.email }}
                                            </a>
                                        </div>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if patient.derniere_visite %}
                                            <span class="badge bg-success">
                                                {{ patient.derniere_visite.strftime('%d/%m/%Y') }}
                                            </span>
                                        {% else %}
                                            <span class="text-muted">Aucune visite</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if patient.prochaine_visite %}
                                            <span class="badge bg-warning">
                                                {{ patient.prochaine_visite.strftime('%d/%m/%Y') }}
                                            </span>
                                        {% else %}
                                            <span class="text-muted">Aucun RDV</span>
                                        {% endif %}
                                    </td>
                                    <td class="text-center">
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ url_for('voir_patient', patient_id=patient.id) }}" 
                                               class="btn btn-outline-primary" title="Voir le dossier">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ url_for('nouveau_rendez_vous') }}?patient_id={{ patient.id }}" 
                                               class="btn btn-outline-success" title="Nouveau RDV">
                                                <i class="fas fa-calendar-plus"></i>
                                            </a>
                                            <button class="btn btn-outline-warning" title="Modifier" 
                                                    onclick="modifierPatient({{ patient.id }})">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-outline-danger" title="Supprimer"
                                                    onclick="supprimerPatient({{ patient.id }}, '{{ patient.nom_complet }}')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <div class="mb-4">
                            <i class="fas fa-user-plus fa-4x text-muted"></i>
                        </div>
                        <h4 class="text-muted mb-3">
                            {% if search %}
                                Aucun patient trouvé pour "{{ search }}"
                            {% else %}
                                Aucun patient enregistré
                            {% endif %}
                        </h4>
                        <p class="text-muted mb-4">
                            {% if search %}
                                Essayez de modifier vos critères de recherche
                            {% else %}
                                Commencez par ajouter votre premier patient
                            {% endif %}
                        </p>
                        <a href="{{ url_for('nouveau_patient') }}" class="btn btn-primary btn-lg">
                            <i class="fas fa-user-plus me-2"></i>Ajouter un patient
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<style>
.avatar-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
}

.bg-pink {
    background-color: #e91e63 !important;
}

.patient-row:hover {
    background-color: rgba(37, 99, 235, 0.05);
    transform: translateX(2px);
    transition: all 0.3s ease;
}

.patient-row:hover .avatar-circle {
    transform: scale(1.1);
    transition: transform 0.3s ease;
}
</style>

<script>
function modifierPatient(patientId) {
    // Rediriger vers la page de modification (à implémenter)
    window.location.href = /patients//modifier;
}

function supprimerPatient(patientId, nomPatient) {
    if (confirm(Êtes-vous sûr de vouloir supprimer le patient  ?\n\nCette action est irréversible et supprimera également tous les rendez-vous associés.)) {
        // Envoyer la requête de suppression (à implémenter)
        fetch(/patients//supprimer, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                IlajiMedical.showNotification('Patient supprimé avec succès', 'success');
                location.reload();
            } else {
                IlajiMedical.showNotification('Erreur lors de la suppression', 'danger');
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            IlajiMedical.showNotification('Erreur lors de la suppression', 'danger');
        });
    }
}

// Recherche en temps réel
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.querySelector('.search-input');
    if (searchInput) {
        searchInput.addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            const rows = document.querySelectorAll('.patient-row');
            
            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        });
    }
});
</script>
{% endblock %}
