﻿#  Ilaji Medical - Système de Gestion de Cabinet Médical

##  Description

**Ilaji Medical** est une application web moderne et complète pour la gestion de cabinet médical. Elle offre une interface intuitive et des fonctionnalités avancées pour optimiser la gestion quotidienne d'un cabinet médical.

##  Fonctionnalités Principales

###  Gestion des Patients
- **Dossiers patients complets** avec informations personnelles, médicales et de contact
- **Historique médical** détaillé avec consultations et traitements
- **Recherche avancée** et filtrage des patients
- **Contact d'urgence** et informations de mutuelle
- **Calcul automatique de l'âge** et suivi des visites

###  Gestion des Médecins
- **Profils médecins** avec spécialités et informations professionnelles
- **Horaires de travail** et disponibilités
- **Tarifs de consultation** par médecin
- **Numéros RPPS/ADELI** et secteur de conventionnement

###  Système de Rendez-vous Avancé
- **Prise de rendez-vous** multiple : en ligne, téléphone, papier, urgence
- **Gestion des priorités** : normale, urgente, très urgente
- **Statuts détaillés** : planifié, confirmé, en cours, terminé, annulé
- **Planning du jour** avec vue d'ensemble
- **Rappels automatiques** et notifications
- **Historique des appels** téléphoniques

###  Tableau de Bord Intelligent
- **Statistiques en temps réel** du cabinet
- **Rendez-vous urgents** mis en évidence
- **Actions rapides** pour les tâches courantes
- **Vue d'ensemble** des activités de la semaine
- **Notifications** et alertes importantes

###  Gestion de la Facturation
- **Facturation automatique** basée sur les consultations
- **Suivi des paiements** (espèces, carte, chèque, virement, tiers payant)
- **Génération de factures** PDF
- **Statistiques financières** du cabinet

###  Interface Moderne
- **Design responsive** adapté à tous les écrans
- **Animations fluides** et interface intuitive
- **Mode sombre** automatique selon les préférences
- **Accessibilité** optimisée
- **Performance** élevée avec chargement rapide

##  Technologies Utilisées

### Backend
- **Python 3.8+** - Langage de programmation
- **Flask 2.3.3** - Framework web léger et puissant
- **SQLAlchemy** - ORM pour la gestion de base de données
- **SQLite/MySQL** - Base de données (configurable)

### Frontend
- **HTML5** - Structure sémantique
- **CSS3** - Styles modernes avec variables CSS
- **Bootstrap 5.3** - Framework CSS responsive
- **JavaScript ES6+** - Interactivité et animations
- **Font Awesome 6** - Icônes vectorielles

### Outils de Développement
- **Python Virtual Environment** - Isolation des dépendances
- **Flask-WTF** - Gestion des formulaires
- **python-dotenv** - Variables d'environnement
- **Pillow** - Traitement d'images
- **ReportLab** - Génération de PDF

##  Installation et Configuration

### Prérequis
- Python 3.8 ou supérieur
- pip (gestionnaire de paquets Python)
- Git (optionnel)

### Installation

1. **Cloner ou télécharger le projet**
`ash
# Si vous avez Git
git clone https://github.com/votre-repo/ilaji-medical.git
cd ilaji-medical

# Ou télécharger et extraire le ZIP
`

2. **Créer un environnement virtuel**
`ash
python -m venv venv

# Activer l'environnement virtuel
# Sur Windows
venv\Scripts\activate

# Sur macOS/Linux
source venv/bin/activate
`

3. **Installer les dépendances**
`ash
pip install -r requirements.txt
`

4. **Configuration de l'environnement**
`ash
# Copier le fichier d'exemple
cp .env.example .env

# Modifier les variables selon vos besoins
# MYSQL_HOST=localhost
# MYSQL_USER=root
# MYSQL_PASSWORD=votre_mot_de_passe
# MYSQL_DB=ilaji_medical
# SECRET_KEY=votre_cle_secrete_unique
`

5. **Initialiser la base de données**
`ash
python app.py
# La base de données sera créée automatiquement au premier lancement
`

6. **Lancer l'application**
`ash
python app.py
`

L'application sera accessible à l'adresse : **http://localhost:5000**

##  Structure du Projet

`
ilaji/
 app.py                 # Application Flask principale
 config.py             # Configuration de l'application
 requirements.txt      # Dépendances Python
 .env                  # Variables d'environnement (à créer)
 README.md             # Documentation du projet

 models/               # Modèles de données
    __init__.py
    patient.py        # Modèle Patient
    medecin.py        # Modèle Médecin
    rendez_vous.py    # Modèle RendezVous
    appel_telephonique.py  # Modèle AppelTelephonique
    dossier_medical.py     # Modèle DossierMedical

 templates/            # Templates HTML
    base.html         # Template de base
    index.html        # Tableau de bord
    planning.html     # Planning du jour
    patients/         # Templates patients
    medecins/         # Templates médecins
    rendez_vous/      # Templates rendez-vous
    appels/           # Templates appels
    dossiers/         # Templates dossiers médicaux
    facturation/      # Templates facturation

 static/               # Fichiers statiques
    css/
       style.css     # Styles personnalisés
    js/
       main.js       # JavaScript personnalisé
    images/           # Images et logos
    uploads/          # Fichiers uploadés

 database/             # Base de données
    __init__.py       # Instance SQLAlchemy
    schema.sql        # Script de création (optionnel)

 venv/                 # Environnement virtuel Python
`

##  Utilisation

### Première Utilisation

1. **Accéder au tableau de bord** : http://localhost:5000
2. **Ajouter des médecins** via le menu "Médecins > Nouveau médecin"
3. **Ajouter des patients** via le menu "Patients > Nouveau patient"
4. **Créer des rendez-vous** via le menu "Rendez-vous > Nouveau RDV"
5. **Consulter le planning** via "Rendez-vous > Planning du jour"

### Fonctionnalités Avancées

- **Recherche rapide** : Utilisez la barre de recherche pour trouver rapidement patients et rendez-vous
- **Filtres** : Filtrez les listes par date, médecin, statut, etc.
- **Actions rapides** : Utilisez les boutons d'action rapide du tableau de bord
- **Notifications** : Surveillez les notifications pour les rendez-vous urgents
- **Impression** : Imprimez les plannings et les listes directement depuis l'interface

##  Configuration Avancée

### Base de Données MySQL

Pour utiliser MySQL au lieu de SQLite :

1. **Installer MySQL** sur votre système
2. **Créer une base de données** :
`sql
CREATE DATABASE ilaji_medical CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
`
3. **Modifier le fichier .env** :
`env
DATABASE_URL=mysql+pymysql://username:password@localhost/ilaji_medical
`

### Personnalisation

- **Logo** : Remplacez les fichiers dans static/images/
- **Couleurs** : Modifiez les variables CSS dans static/css/style.css
- **Fonctionnalités** : Ajoutez de nouveaux modèles dans models/

##  Contribution

Les contributions sont les bienvenues ! Pour contribuer :

1. **Fork** le projet
2. **Créer une branche** pour votre fonctionnalité
3. **Commiter** vos changements
4. **Pousser** vers la branche
5. **Ouvrir une Pull Request**

##  Licence

Ce projet est sous licence MIT. Voir le fichier LICENSE pour plus de détails.

##  Support

Pour obtenir de l'aide :

- **Documentation** : Consultez ce README
- **Issues** : Ouvrez une issue sur GitHub
- **Email** : <EMAIL>

##  Mises à Jour

### Version 2.0 (Actuelle)
-  Interface moderne avec Bootstrap 5
-  Gestion avancée des rendez-vous
-  Système de priorités et statuts
-  Tableau de bord intelligent
-  Recherche et filtres avancés
-  Animations et transitions fluides

### Prochaines Versions
-  Authentification et gestion des utilisateurs
-  Système de notifications push
-  API REST pour intégrations
-  Application mobile
-  Sauvegarde automatique cloud
-  Rapports et statistiques avancés

---

**Ilaji Medical** - *Simplifiez la gestion de votre cabinet médical* 
