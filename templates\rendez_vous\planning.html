{% extends "base.html" %}

{% block title %}Planning du jour - Ilaji Medical{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="display-6 text-primary mb-1">
                    <i class="fas fa-calendar-day me-3"></i>Planning du jour
                </h1>
                <p class="text-muted mb-0">{{ date.strftime('%A %d %B %Y') }}</p>
            </div>
            <div>
                <a href="{{ url_for('nouveau_rendez_vous') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>Nouveau RDV
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-gradient-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-clock me-2"></i>Rendez-vous du jour ({{ rendez_vous|length }})
                </h5>
            </div>
            <div class="card-body">
                {% if rendez_vous %}
                    <div class="timeline">
                        {% for rdv in rendez_vous %}
                        <div class="timeline-item mb-4">
                            <div class="row">
                                <div class="col-md-2">
                                    <div class="time-badge bg-primary text-white text-center p-2 rounded">
                                        <strong>{{ rdv.date_rdv.strftime('%H:%M') }}</strong>
                                        <br>
                                        <small>{{ rdv.heure_fin_formatee }}</small>
                                    </div>
                                </div>
                                <div class="col-md-10">
                                    <div class="card border-start border-4 border-{{ 'danger' if rdv.priorite == 'tres_urgente' else 'warning' if rdv.priorite == 'urgente' else 'primary' }}">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between align-items-start">
                                                <div>
                                                    <h6 class="card-title mb-1">
                                                        {% if rdv.patient %}
                                                            {{ rdv.patient.nom_complet }}
                                                        {% else %}
                                                            <span class="text-muted">Patient supprimé</span>
                                                        {% endif %}
                                                        {% if rdv.priorite != 'normale' %}
                                                            <span class="badge bg-{{ 'danger' if rdv.priorite == 'tres_urgente' else 'warning' }} ms-2">
                                                                {{ 'TRÈS URGENT' if rdv.priorite == 'tres_urgente' else 'URGENT' }}
                                                            </span>
                                                        {% endif %}
                                                    </h6>
                                                    <p class="card-text mb-1">
                                                        <i class="fas fa-user-md me-1"></i>
                                                        {% if rdv.medecin %}
                                                            {{ rdv.medecin.nom_complet }} - {{ rdv.medecin.specialite }}
                                                        {% else %}
                                                            <span class="text-muted">Médecin supprimé</span>
                                                        {% endif %}
                                                    </p>
                                                    {% if rdv.motif %}
                                                    <p class="card-text mb-1">
                                                        <i class="fas fa-notes-medical me-1"></i>{{ rdv.motif }}
                                                    </p>
                                                    {% endif %}
                                                    {% if rdv.patient and rdv.patient.telephone %}
                                                    <p class="card-text mb-0">
                                                        <i class="fas fa-phone me-1"></i>{{ rdv.patient.telephone }}
                                                    </p>
                                                    {% endif %}
                                                </div>
                                                <div class="text-end">
                                                    <span class="badge bg-{{ 'success' if rdv.statut == 'confirme' else 'primary' if rdv.statut == 'planifie' else 'info' if rdv.statut == 'en_cours' else 'secondary' if rdv.statut == 'termine' else 'danger' }} mb-2">
                                                        {{ rdv.statut.replace('_', ' ').title() }}
                                                    </span>
                                                    <br>
                                                    <div class="btn-group btn-group-sm">
                                                        <button class="btn btn-outline-success" title="Commencer consultation">
                                                            <i class="fas fa-play"></i>
                                                        </button>
                                                        <button class="btn btn-outline-primary" title="Voir détails">
                                                            <i class="fas fa-eye"></i>
                                                        </button>
                                                        <button class="btn btn-outline-warning" title="Modifier">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <button class="btn btn-outline-danger" title="Annuler">
                                                            <i class="fas fa-times"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-calendar-day fa-4x text-muted mb-3"></i>
                        <h4 class="text-muted">Aucun rendez-vous aujourd'hui</h4>
                        <p class="text-muted">Profitez de cette journée libre ou planifiez de nouveaux rendez-vous</p>
                        <a href="{{ url_for('nouveau_rendez_vous') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Planifier un rendez-vous
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<style>
.time-badge {
    min-width: 80px;
}

.timeline-item {
    position: relative;
}

.timeline-item:not(:last-child)::after {
    content: '';
    position: absolute;
    left: 40px;
    top: 80px;
    width: 2px;
    height: calc(100% - 60px);
    background: #dee2e6;
}
</style>
{% endblock %}
