{% extends "base.html" %}

{% block title %}Mon profil - Ilaji Medical{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="display-6 text-primary mb-1">
                    <i class="fas fa-user me-3"></i>Mon profil
                </h1>
                <p class="text-muted mb-0">Gérez vos informations personnelles</p>
            </div>
            <div class="text-end">
                <div class="badge bg-{{ 'danger' if current_user.role.value == 'admin' else 'success' if current_user.role.value == 'medecin' else 'warning' if current_user.role.value == 'secretaire' else 'info' }} fs-6 px-3 py-2">
                    <i class="fas fa-{{ 'crown' if current_user.role.value == 'admin' else 'user-md' if current_user.role.value == 'medecin' else 'user-tie' if current_user.role.value == 'secretaire' else 'user' }} me-2"></i>
                    {{ current_user.role.value.title() }}
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8 mx-auto">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-gradient-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-edit me-2"></i>Informations personnelles
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" novalidate>
                    {{ form.hidden_tag() }}
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.prenom.label(class="form-label") }}
                            {{ form.prenom(class="form-control" + (" is-invalid" if form.prenom.errors else "")) }}
                            {% if form.prenom.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.prenom.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        <div class="col-md-6 mb-3">
                            {{ form.nom.label(class="form-label") }}
                            {{ form.nom(class="form-control" + (" is-invalid" if form.nom.errors else "")) }}
                            {% if form.nom.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.nom.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        {{ form.email.label(class="form-label") }}
                        {{ form.email(class="form-control" + (" is-invalid" if form.email.errors else "")) }}
                        {% if form.email.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.email.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.telephone.label(class="form-label") }}
                        {{ form.telephone(class="form-control") }}
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('auth.change_password') }}" class="btn btn-outline-warning">
                            <i class="fas fa-key me-2"></i>Changer le mot de passe
                        </a>
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Informations du compte -->
        <div class="card border-0 shadow-sm mt-4">
            <div class="card-header bg-gradient-info text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>Informations du compte
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>Nom d'utilisateur:</strong> {{ current_user.username }}</p>
                        <p><strong>Rôle:</strong> 
                            <span class="badge bg-{{ 'danger' if current_user.role.value == 'admin' else 'success' if current_user.role.value == 'medecin' else 'warning' if current_user.role.value == 'secretaire' else 'info' }}">
                                {{ current_user.role.value.title() }}
                            </span>
                        </p>
                        <p><strong>Statut:</strong> 
                            <span class="badge bg-{{ 'success' if current_user.is_active else 'danger' }}">
                                {{ 'Actif' if current_user.is_active else 'Inactif' }}
                            </span>
                        </p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>Compte créé le:</strong> {{ current_user.date_creation.strftime('%d/%m/%Y à %H:%M') if current_user.date_creation else 'Non disponible' }}</p>
                        <p><strong>Dernière connexion:</strong> {{ current_user.derniere_connexion.strftime('%d/%m/%Y à %H:%M') if current_user.derniere_connexion else 'Jamais' }}</p>
                        <p><strong>Email vérifié:</strong> 
                            <span class="badge bg-{{ 'success' if current_user.is_verified else 'warning' }}">
                                {{ 'Oui' if current_user.is_verified else 'Non' }}
                            </span>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Validation en temps réel de l'email
    const emailField = document.getElementById('email');
    if (emailField) {
        emailField.addEventListener('blur', function() {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            const isValid = emailRegex.test(this.value);
            
            this.classList.remove('is-valid', 'is-invalid');
            if (this.value && isValid) {
                this.classList.add('is-valid');
            } else if (this.value && !isValid) {
                this.classList.add('is-invalid');
            }
        });
    }
    
    // Animation du bouton de soumission
    const submitBtn = document.querySelector('button[type="submit"]');
    if (submitBtn) {
        submitBtn.addEventListener('click', function(e) {
            if (this.form.checkValidity()) {
                this.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Mise à jour...';
                this.disabled = true;
            }
        });
    }
});
</script>
{% endblock %}
