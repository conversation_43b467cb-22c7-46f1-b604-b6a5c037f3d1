{% extends "base.html" %}

{% block title %}Gestion du Personnel - Ilaji Medical{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="display-6 text-success mb-1">
                    <i class="fas fa-users-cog me-3"></i>Gestion du Personnel
                </h1>
                <p class="text-muted mb-0">Administration des comptes médecins et secrétaires</p>
            </div>
            <div>
                <a href="/admin/creer-compte-personnel" class="btn btn-success">
                    <i class="fas fa-user-plus me-2"></i>Nouveau Compte
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Statistiques -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <i class="fas fa-user-md fa-2x text-success mb-2"></i>
                <h4 class="text-success">{{ medecins|length }}</h4>
                <p class="text-muted mb-0">Médecins</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <i class="fas fa-user-tie fa-2x text-info mb-2"></i>
                <h4 class="text-info">{{ secretaires|length }}</h4>
                <p class="text-muted mb-0">Secrétaires</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <i class="fas fa-envelope fa-2x text-warning mb-2"></i>
                <h4 class="text-warning">{{ (medecins|selectattr("statut_compte", "equalto", "invitation_envoyee")|list|length) + (secretaires|selectattr("statut_compte", "equalto", "invitation_envoyee")|list|length) }}</h4>
                <p class="text-muted mb-0">Invitations envoyées</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 shadow-sm">
            <div class="card-body text-center">
                <i class="fas fa-check-circle fa-2x text-primary mb-2"></i>
                <h4 class="text-primary">{{ (medecins|selectattr("statut_compte", "equalto", "actif")|list|length) + (secretaires|selectattr("statut_compte", "equalto", "actif")|list|length) }}</h4>
                <p class="text-muted mb-0">Comptes actifs</p>
            </div>
        </div>
    </div>
</div>

<!-- Médecins -->
<div class="card border-0 shadow-sm mb-4">
    <div class="card-header bg-gradient-success text-white">
        <h5 class="card-title mb-0">
            <i class="fas fa-user-md me-2"></i>Médecins ({{ medecins|length }})
        </h5>
    </div>
    <div class="card-body">
        {% if medecins %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-light">
                    <tr>
                        <th>Médecin</th>
                        <th>Spécialité</th>
                        <th>Contact</th>
                        <th>Statut Compte</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for medecin in medecins %}
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="bg-success bg-gradient rounded-circle p-2 me-3">
                                    <i class="fas fa-user-md text-white"></i>
                                </div>
                                <div>
                                    <strong>Dr. {{ medecin.prenom }} {{ medecin.nom }}</strong><br>
                                    <small class="text-muted">{{ medecin.username }}</small>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="badge bg-success">{{ medecin.specialite }}</span><br>
                            <small class="text-muted">{{ medecin.tarif_consultation }}€/consultation</small>
                        </td>
                        <td>
                            <div class="small">
                                <i class="fas fa-envelope me-1"></i>{{ medecin.email }}<br>
                                <i class="fas fa-phone me-1"></i>{{ medecin.telephone }}
                            </div>
                        </td>
                        <td>
                            {% if medecin.statut_compte == 'invitation_envoyee' %}
                                <span class="badge bg-warning">Invitation envoyée</span>
                            {% elif medecin.statut_compte == 'actif' %}
                                <span class="badge bg-success">Actif</span>
                            {% else %}
                                <span class="badge bg-secondary">En attente</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="/admin/envoyer-invitation/medecin/{{ medecin.id }}" class="btn btn-outline-info" title="Renvoyer invitation">
                                    <i class="fas fa-paper-plane"></i>
                                </a>
                                <a href="/admin/reset-password/{{ medecin.id }}" class="btn btn-outline-warning" title="Reset password">
                                    <i class="fas fa-key"></i>
                                </a>
                                <a href="/admin/medecin/{{ medecin.id }}" class="btn btn-outline-primary" title="Voir détails">
                                    <i class="fas fa-eye"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-4">
            <i class="fas fa-user-md fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">Aucun médecin enregistré</h5>
            <p class="text-muted">Créez des comptes pour vos médecins.</p>
        </div>
        {% endif %}
    </div>
</div>

<!-- Secrétaires -->
<div class="card border-0 shadow-sm">
    <div class="card-header bg-gradient-info text-white">
        <h5 class="card-title mb-0">
            <i class="fas fa-user-tie me-2"></i>Secrétaires ({{ secretaires|length }})
        </h5>
    </div>
    <div class="card-body">
        {% if secretaires %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-light">
                    <tr>
                        <th>Secrétaire</th>
                        <th>Poste</th>
                        <th>Contact</th>
                        <th>Statut Compte</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for secretaire in secretaires %}
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="bg-info bg-gradient rounded-circle p-2 me-3">
                                    <i class="fas fa-user-tie text-white"></i>
                                </div>
                                <div>
                                    <strong>{{ secretaire.prenom }} {{ secretaire.nom }}</strong><br>
                                    <small class="text-muted">{{ secretaire.username }}</small>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="badge bg-info">{{ secretaire.poste }}</span><br>
                            <small class="text-muted">Service: {{ secretaire.service }}</small>
                        </td>
                        <td>
                            <div class="small">
                                <i class="fas fa-envelope me-1"></i>{{ secretaire.email }}<br>
                                <i class="fas fa-phone me-1"></i>{{ secretaire.telephone }}
                            </div>
                        </td>
                        <td>
                            {% if secretaire.statut_compte == 'invitation_envoyee' %}
                                <span class="badge bg-warning">Invitation envoyée</span><br>
                                <small class="text-muted">{{ secretaire.date_invitation }}</small>
                            {% elif secretaire.statut_compte == 'actif' %}
                                <span class="badge bg-success">Actif</span>
                            {% else %}
                                <span class="badge bg-secondary">En attente</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="/admin/envoyer-invitation/secretaire/{{ secretaire.id }}" class="btn btn-outline-info" title="Renvoyer invitation">
                                    <i class="fas fa-paper-plane"></i>
                                </a>
                                {% if secretaire.password %}
                                <a href="/admin/reset-password/{{ secretaire.id }}" class="btn btn-outline-warning" title="Reset password">
                                    <i class="fas fa-key"></i>
                                </a>
                                {% endif %}
                                <button class="btn btn-outline-primary" onclick="voirToken('{{ secretaire.token_invitation }}')" title="Voir token">
                                    <i class="fas fa-qrcode"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-4">
            <i class="fas fa-user-tie fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">Aucune secrétaire enregistrée</h5>
            <p class="text-muted">Créez des comptes pour vos secrétaires.</p>
        </div>
        {% endif %}
    </div>
</div>

<script>
function voirToken(token) {
    if (token) {
        alert(`Token d'invitation :\n${token}\n\nLien d'inscription :\nhttp://127.0.0.1:5000/inscription/secretaire?token=${token}`);
    } else {
        alert('Aucun token généré pour cette secrétaire.');
    }
}
</script>
{% endblock %}
