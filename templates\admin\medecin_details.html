{% extends "base.html" %}

{% block title %}Détails Médecin - Ilaji Medical{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="display-6 text-success mb-1">
                    <i class="fas fa-user-md me-3"></i>Détails Médecin
                </h1>
                <p class="text-muted mb-0">Profil professionnel de Dr. {{ medecin.prenom }} {{ medecin.nom }}</p>
            </div>
            <div>
                <a href="/admin/medecins" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Retour à la liste
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Informations principales -->
    <div class="col-lg-8">
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-gradient-success text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user-md me-2"></i>Informations professionnelles
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted">Prénom</label>
                            <p class="fw-bold">Dr. {{ medecin.prenom }}</p>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label text-muted">Nom</label>
                            <p class="fw-bold">{{ medecin.nom }}</p>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label text-muted">Spécialité</label>
                            <p>
                                <span class="badge bg-success fs-6">
                                    <i class="fas fa-stethoscope me-1"></i>{{ medecin.specialite }}
                                </span>
                            </p>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label text-muted">Tarif consultation</label>
                            <p class="fw-bold text-success">
                                <i class="fas fa-euro-sign me-2"></i>
                                {{ medecin.tarif_consultation }}€
                            </p>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted">Email</label>
                            <p class="fw-bold">
                                <i class="fas fa-envelope me-2 text-primary"></i>
                                <a href="mailto:{{ medecin.email }}" class="text-decoration-none">{{ medecin.email }}</a>
                            </p>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label text-muted">Téléphone</label>
                            <p class="fw-bold">
                                <i class="fas fa-phone me-2 text-success"></i>
                                <a href="tel:{{ medecin.telephone }}" class="text-decoration-none">{{ medecin.telephone }}</a>
                            </p>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label text-muted">Numéro RPPS</label>
                            <p class="fw-bold">
                                <i class="fas fa-id-badge me-2 text-info"></i>
                                {{ medecin.numero_rpps if medecin.numero_rpps else 'Non renseigné' }}
                            </p>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label text-muted">Secteur conventionnement</label>
                            <p class="fw-bold">
                                <i class="fas fa-certificate me-2 text-warning"></i>
                                {{ medecin.secteur_conventionnement if medecin.secteur_conventionnement else 'Non spécifié' }}
                            </p>
                        </div>
                    </div>
                </div>
                
                {% if medecin.adresse %}
                <div class="row">
                    <div class="col-12">
                        <div class="mb-3">
                            <label class="form-label text-muted">Adresse du cabinet</label>
                            <p class="fw-bold">
                                <i class="fas fa-map-marker-alt me-2 text-danger"></i>
                                {{ medecin.adresse }}
                            </p>
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Planning et disponibilités -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-calendar me-2"></i>Planning et disponibilités
                </h5>
            </div>
            <div class="card-body">
                <div class="text-center py-4">
                    <i class="fas fa-calendar-alt fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">Planning non configuré</h5>
                    <p class="text-muted">Le planning du médecin apparaîtra ici une fois configuré.</p>
                    <button class="btn btn-primary" onclick="alert('Fonctionnalité en cours de développement')">
                        <i class="fas fa-cog me-2"></i>Configurer le planning
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Actions rapides -->
    <div class="col-lg-4">
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-tools me-2"></i>Actions rapides
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="/admin/medecin/{{ medecin.id }}/modifier" class="btn btn-warning">
                        <i class="fas fa-edit me-2"></i>Modifier les informations
                    </a>
                    
                    <a href="/nouveau-rendez-vous?medecin={{ medecin.id }}" class="btn btn-success">
                        <i class="fas fa-calendar-plus me-2"></i>Nouveau rendez-vous
                    </a>
                    
                    <a href="/admin/planning-medecin/{{ medecin.id }}" class="btn btn-info">
                        <i class="fas fa-calendar me-2"></i>Voir planning
                    </a>

                    <a href="/admin/statistiques-medecin/{{ medecin.id }}" class="btn btn-secondary">
                        <i class="fas fa-chart-line me-2"></i>Statistiques
                    </a>

                    <hr>

                    <a href="/admin/reset-password/{{ medecin.id }}" class="btn btn-warning">
                        <i class="fas fa-key me-2"></i>Réinitialiser mot de passe
                    </a>

                    <a href="/admin/historique-connexions/{{ medecin.id }}" class="btn btn-info">
                        <i class="fas fa-history me-2"></i>Historique connexions
                    </a>

                    <hr>
                    
                    <button class="btn btn-outline-danger" onclick="confirmerSuppression({{ medecin.id }}, '{{ medecin.prenom }} {{ medecin.nom }}')">
                        <i class="fas fa-trash me-2"></i>Supprimer le médecin
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Statistiques médecin -->
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>Statistiques
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6 mb-3">
                        <div class="border-end">
                            <h4 class="text-primary mb-1">0</h4>
                            <small class="text-muted">Consultations ce mois</small>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <h4 class="text-success mb-1">0</h4>
                        <small class="text-muted">Patients suivis</small>
                    </div>
                    <div class="col-6">
                        <div class="border-end">
                            <h4 class="text-warning mb-1">0</h4>
                            <small class="text-muted">RDV à venir</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h4 class="text-info mb-1">0€</h4>
                        <small class="text-muted">Revenus ce mois</small>
                    </div>
                </div>
                
                <hr>
                
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-1">
                        <span class="small">Taux d'occupation</span>
                        <span class="small text-success fw-bold">0%</span>
                    </div>
                    <div class="progress" style="height: 8px;">
                        <div class="progress-bar bg-success" style="width: 0%"></div>
                    </div>
                </div>
                
                <div class="text-center">
                    <small class="text-muted">
                        <i class="fas fa-user-plus me-1"></i>
                        Médecin depuis : {{ medecin.date_creation if medecin.date_creation else 'Récemment' }}
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function confirmerSuppression(id, nom) {
    if (confirm(`Êtes-vous sûr de vouloir supprimer le médecin "${nom}" ?\n\nCette action supprimera également tous les rendez-vous associés.`)) {
        window.location.href = `/admin/medecin/${id}/supprimer`;
    }
}
</script>
{% endblock %}
