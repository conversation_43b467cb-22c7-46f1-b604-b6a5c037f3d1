from flask import Flask, render_template, request, redirect, url_for, flash, jsonify
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager, login_required, current_user
from flask_bcrypt import Bcrypt
from datetime import datetime, date, timedelta
import os

# Import de la configuration
from config import Config

# Import de l'instance db
from database import db

# Import des blueprints (temporairement désactivé)
# from auth import auth_bp

# Import des modèles (temporairement désactivé)
# from models.user import User, UserRole

# Import des décorateurs (temporairement désactivé)
# from decorators import login_required_with_role, admin_required, staff_required

def create_app():
    app = Flask(__name__)
    app.config.from_object(Config)

    # Initialisation de la base de données
    db.init_app(app)

    # Initialisation de Flask-Login (temporairement désactivé)
    # login_manager = LoginManager()
    # login_manager.init_app(app)
    # login_manager.login_view = 'auth.login'
    # login_manager.login_message = 'Vous devez être connecté pour accéder à cette page.'
    # login_manager.login_message_category = 'warning'

    # Initialisation de Bcrypt (temporairement désactivé)
    # bcrypt = Bcrypt()
    # bcrypt.init_app(app)

    # @login_manager.user_loader
    # def load_user(user_id):
    #     return User.query.get(int(user_id))

    # Enregistrement des blueprints (temporairement désactivé)
    # app.register_blueprint(auth_bp)

    # Création des tables si elles n'existent pas
    with app.app_context():
        db.create_all()

        # Création d'un utilisateur admin par défaut (temporairement désactivé)
        # admin_user = User.query.filter_by(email='<EMAIL>').first()
        # if not admin_user:
        #     admin_user = User(
        #         email='<EMAIL>',
        #         username='admin',
        #         nom='Administrateur',
        #         prenom='Système',
        #         role=UserRole.ADMIN,
        #         is_active=True,
        #         is_verified=True
        #     )
        #     admin_user.set_password('admin123')
        #     db.session.add(admin_user)
        #     db.session.commit()
        #     print("Utilisateur admin créé: <EMAIL> / admin123")

    return app

app = create_app()

@app.route('/')
def index():
    """Page d'accueil - redirection vers la page d'accueil"""
    return render_template('auth/welcome.html')

@app.route('/auth/role-choice')
def role_choice():
    """Page de choix entre connexion et inscription selon le rôle"""
    role = request.args.get('role', 'patient')
    # Vérifier que le rôle est valide
    valid_roles = ['admin', 'medecin', 'secretaire', 'patient']
    if role not in valid_roles:
        role = 'patient'
    return render_template('auth/role_choice.html', role=role)

@app.route('/auth/staff-login', methods=['GET', 'POST'])
def staff_login():
    """Page de connexion pour médecins et secrétaires"""
    role = request.args.get('role') or request.form.get('role', 'medecin')

    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')

        # Simulation de vérification des identifiants
        # TODO: Remplacer par une vraie vérification en base de données
        valid_credentials = {
            'medecin': {'dr.martin': 'medecin123', 'dr.durand': 'medecin456'},
            'secretaire': {'marie.sec': 'secret123', 'julie.admin': 'admin456'},
            'admin': {'mehdiallaoui': 'mehdi123'}
        }

        if role in valid_credentials and username in valid_credentials[role]:
            if valid_credentials[role][username] == password:
                dashboards = {
                    'admin': '/admin/dashboard',
                    'medecin': '/medecin/dashboard',
                    'secretaire': '/secretaire/dashboard'
                }
                flash(f'Connexion réussie ! Bienvenue {username}', 'success')
                return redirect(dashboards.get(role, '/dashboard'))
            else:
                flash('Mot de passe incorrect', 'error')
        else:
            flash('Nom d\'utilisateur non trouvé', 'error')

    return render_template('auth/staff_login.html', role=role)

@app.route('/auth/patient-access', methods=['GET', 'POST'])
def patient_access():
    """Page d'accès pour les patients avec CIN"""
    if request.method == 'POST':
        action = request.form.get('action')

        if action == 'login':
            cin = request.form.get('cin')

            # Simulation de vérification CIN
            # TODO: Remplacer par une vraie vérification en base de données
            valid_cins = ['12345678', '87654321', '11223344', '99887766']

            if cin in valid_cins:
                flash(f'Accès autorisé pour le CIN {cin}', 'success')
                return redirect('/patient/dashboard')
            else:
                flash('CIN non trouvé. Vérifiez votre numéro ou créez un nouveau dossier.', 'error')

        elif action == 'register':
            flash('Redirection vers la création de dossier patient', 'success')
            return redirect('/auth/patient-register')

    return render_template('auth/patient_access.html')

@app.route('/auth/patient-register')
def patient_register():
    """Page de création de dossier patient"""
    flash('Formulaire de création de dossier patient (à implémenter)', 'info')
    return redirect('/patient/dashboard')

@app.route('/dashboard')
def dashboard():
    """Tableau de bord principal (pour les utilisateurs connectés)"""
    try:
        # Pour l'instant, utilisons des données de démonstration
        # jusqu'à ce que les modèles soient corrigés
        total_patients = 0
        total_medecins = 0
        total_rdv_aujourd_hui = 0
        rdv_semaine = 0
        rdv_urgents = []
        prochains_rdv = []

        # Date actuelle formatée
        from datetime import datetime
        import locale
        try:
            locale.setlocale(locale.LC_TIME, 'fr_FR.UTF-8')
        except:
            pass  # Si la locale française n'est pas disponible

        date_actuelle = datetime.now().strftime('%A %d %B %Y')

        return render_template('index.html',
                             total_patients=total_patients,
                             total_medecins=total_medecins,
                             total_rdv_aujourd_hui=total_rdv_aujourd_hui,
                             rdv_semaine=rdv_semaine,
                             rdv_urgents=rdv_urgents,
                             prochains_rdv=prochains_rdv,
                             date_actuelle=date_actuelle)
    except Exception as e:
        flash(f'Erreur lors du chargement du tableau de bord: {str(e)}', 'error')
        return render_template('index.html',
                             total_patients=0,
                             total_medecins=0,
                             total_rdv_aujourd_hui=0,
                             rdv_semaine=0,
                             rdv_urgents=[],
                             prochains_rdv=[],
                             date_actuelle=datetime.now().strftime('%A %d %B %Y'))

# ===== DASHBOARDS PAR RÔLE =====

@app.route('/admin/dashboard')
def admin_dashboard():
    """Dashboard administrateur"""
    try:
        # Statistiques pour l'admin
        stats = {
            'total_users': 0,  # User.query.count(),
            'total_medecins': 0,  # User.query.filter_by(role=UserRole.MEDECIN).count(),
            'rdv_aujourd_hui': 0,  # À implémenter
            'revenus_mois': '€0',  # À implémenter
            'users_evolution': {
                'labels': ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Jun'],
                'data': [10, 15, 25, 30, 45, 50]
            },
            'roles_distribution': {
                'labels': ['Patients', 'Médecins', 'Secrétaires', 'Admins'],
                'data': [25, 5, 3, 1]  # Données de démonstration
            }
        }

        recent_activities = []  # À implémenter
        system_alerts = []  # À implémenter

        return render_template('dashboards/admin.html',
                             stats=stats,
                             recent_activities=recent_activities,
                             system_alerts=system_alerts)
    except Exception as e:
        flash(f'Erreur lors du chargement du dashboard: {str(e)}', 'error')
        return render_template('dashboards/admin.html', stats={}, recent_activities=[], system_alerts=[])

@app.route('/medecin/dashboard')
def medecin_dashboard():
    """Dashboard médecin"""
    stats = {
        'mes_patients': 45,
        'consultations_jour': 8,
        'prescriptions_mois': 23,
        'urgences': 2,
        'users_evolution': {
            'labels': ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Jun'],
            'data': [5, 8, 12, 15, 18, 23]
        },
        'roles_distribution': {
            'labels': ['Consultations', 'Prescriptions', 'Examens', 'Urgences'],
            'data': [45, 23, 12, 8]
        }
    }

    # Activités récentes spécifiques au médecin
    recent_activities = [
        {
            'title': 'Consultation terminée',
            'description': 'Patient Ahmed Ben Ali - Consultation de routine',
            'timestamp': 'Il y a 15 minutes',
            'type_color': 'success',
            'icon': 'user-check'
        },
        {
            'title': 'Prescription émise',
            'description': 'Antibiotiques prescrits pour Fatima Zahra',
            'timestamp': 'Il y a 1 heure',
            'type_color': 'info',
            'icon': 'prescription'
        },
        {
            'title': 'Rendez-vous programmé',
            'description': 'Suivi post-opératoire - Mohamed Alami',
            'timestamp': 'Il y a 2 heures',
            'type_color': 'warning',
            'icon': 'calendar-plus'
        }
    ]

    return render_template('dashboards/medecin.html', stats=stats, recent_activities=recent_activities, system_alerts=[])

@app.route('/secretaire/dashboard')
def secretaire_dashboard():
    """Dashboard secrétaire"""
    stats = {
        'rdv_aujourd_hui': 12,
        'appels_jour': 28,
        'nouveaux_patients': 5,
        'en_attente': 3,
        'users_evolution': {
            'labels': ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Jun'],
            'data': [8, 12, 18, 22, 35, 40]
        },
        'roles_distribution': {
            'labels': ['RDV planifiés', 'RDV confirmés', 'RDV annulés', 'En attente'],
            'data': [15, 12, 2, 3]
        }
    }

    # Activités récentes spécifiques au secrétaire
    recent_activities = [
        {
            'title': 'RDV programmé',
            'description': 'Mme Fatima Benali - Dr. Martin - 16h00',
            'timestamp': 'Il y a 10 minutes',
            'type_color': 'success',
            'icon': 'calendar-plus'
        },
        {
            'title': 'Patient enregistré',
            'description': 'Nouveau patient: M. Youssef Alami',
            'timestamp': 'Il y a 30 minutes',
            'type_color': 'info',
            'icon': 'user-plus'
        },
        {
            'title': 'Appel traité',
            'description': 'Demande de renseignements - Mme Khadija',
            'timestamp': 'Il y a 45 minutes',
            'type_color': 'warning',
            'icon': 'phone'
        }
    ]

    return render_template('dashboards/secretaire.html', stats=stats, recent_activities=recent_activities, system_alerts=[])

@app.route('/patient/dashboard')
def patient_dashboard():
    """Dashboard patient"""
    stats = {
        'prochains_rdv': 2,
        'prescriptions_actives': 3,
        'resultats_attente': 1,
        'rappels': 2,
        'users_evolution': {
            'labels': ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Jun'],
            'data': [1, 2, 1, 3, 2, 4]
        },
        'roles_distribution': {
            'labels': ['Consultations', 'Prescriptions', 'Analyses', 'Vaccins'],
            'data': [8, 5, 3, 2]
        }
    }

    # Activités récentes spécifiques au patient
    recent_activities = [
        {
            'title': 'Rendez-vous confirmé',
            'description': 'RDV avec Dr. Martin le 25/12/2024 à 14h30',
            'timestamp': 'Il y a 2 heures',
            'type_color': 'success',
            'icon': 'calendar-check'
        },
        {
            'title': 'Résultats disponibles',
            'description': 'Analyses sanguines - Résultats normaux',
            'timestamp': 'Hier',
            'type_color': 'info',
            'icon': 'vial'
        },
        {
            'title': 'Prescription renouvelée',
            'description': 'Traitement hypertension - 30 jours',
            'timestamp': 'Il y a 3 jours',
            'type_color': 'warning',
            'icon': 'pills'
        }
    ]

    return render_template('dashboards/patient.html', stats=stats, recent_activities=recent_activities, system_alerts=[])

# ===== ROUTES PROTÉGÉES =====

@app.route('/patients')
def patients():
    """Liste des patients"""
    try:
        # Pour l'instant, liste vide jusqu'à correction des modèles
        patients = []
        return render_template('patients/liste.html', patients=patients)
    except Exception as e:
        flash(f'Erreur lors du chargement des patients: {str(e)}', 'error')
        return render_template('patients/liste.html', patients=[])

# ===== ROUTES MÉDECIN =====

@app.route('/mes-patients')
def mes_patients():
    """Liste des patients du médecin connecté"""
    flash('Fonctionnalité en cours de développement', 'info')
    return render_template('en_developpement.html')

@app.route('/mon-planning')
def mon_planning():
    """Planning personnel du médecin"""
    flash('Fonctionnalité en cours de développement', 'info')
    return render_template('en_developpement.html')

@app.route('/mes-prescriptions')
def mes_prescriptions():
    """Prescriptions du médecin"""
    flash('Fonctionnalité en cours de développement', 'info')
    return render_template('en_developpement.html')

@app.route('/urgences')
def urgences():
    """Cas urgents"""
    flash('Fonctionnalité en cours de développement', 'info')
    return render_template('en_developpement.html')

@app.route('/nouvelle-consultation', methods=['GET', 'POST'])
def nouvelle_consultation():
    """Nouvelle consultation"""
    if request.method == 'POST':
        flash('Consultation enregistrée avec succès', 'success')
        return redirect('/medecin/dashboard')
    flash('Fonctionnalité en cours de développement', 'info')
    return render_template('en_developpement.html')

@app.route('/nouvelle-prescription', methods=['GET', 'POST'])
def nouvelle_prescription():
    """Nouvelle prescription"""
    if request.method == 'POST':
        flash('Prescription créée avec succès', 'success')
        return redirect('/medecin/dashboard')
    flash('Fonctionnalité en cours de développement', 'info')
    return render_template('en_developpement.html')

@app.route('/rechercher-patient', methods=['GET', 'POST'])
def rechercher_patient():
    """Rechercher un patient"""
    if request.method == 'POST':
        flash('Recherche effectuée', 'info')
    flash('Fonctionnalité en cours de développement', 'info')
    return render_template('en_developpement.html')

@app.route('/mon-agenda')
def mon_agenda():
    """Agenda personnel du médecin"""
    flash('Fonctionnalité en cours de développement', 'info')
    return render_template('en_developpement.html')

# ===== ROUTES PATIENT =====

@app.route('/mes-rendez-vous')
def mes_rendez_vous():
    """Rendez-vous du patient"""
    flash('Fonctionnalité en cours de développement', 'info')
    return render_template('en_developpement.html')

@app.route('/mes-resultats')
def mes_resultats():
    """Résultats d'analyses du patient"""
    flash('Fonctionnalité en cours de développement', 'info')
    return render_template('en_developpement.html')

@app.route('/mes-rappels')
def mes_rappels():
    """Rappels du patient"""
    flash('Fonctionnalité en cours de développement', 'info')
    return render_template('en_developpement.html')

@app.route('/prendre-rdv', methods=['GET', 'POST'])
def prendre_rdv():
    """Prendre un rendez-vous"""
    if request.method == 'POST':
        flash('Demande de rendez-vous envoyée avec succès', 'success')
        return redirect('/patient/dashboard')
    flash('Fonctionnalité en cours de développement', 'info')
    return render_template('en_developpement.html')

@app.route('/mon-dossier')
def mon_dossier():
    """Dossier médical complet du patient"""
    flash('Fonctionnalité en cours de développement', 'info')
    return render_template('en_developpement.html')

@app.route('/mes-documents')
def mes_documents():
    """Documents du patient"""
    flash('Fonctionnalité en cours de développement', 'info')
    return render_template('en_developpement.html')

@app.route('/contact-medecin', methods=['GET', 'POST'])
def contact_medecin():
    """Contacter le médecin"""
    if request.method == 'POST':
        flash('Message envoyé au médecin avec succès', 'success')
        return redirect('/patient/dashboard')
    flash('Fonctionnalité en cours de développement', 'info')
    return render_template('en_developpement.html')

# ===== ROUTES SECRÉTAIRE =====

@app.route('/planning-jour')
def planning_jour():
    """Planning du jour pour secrétaire"""
    flash('Fonctionnalité en cours de développement', 'info')
    return render_template('en_developpement.html')

@app.route('/journal-appels')
def journal_appels():
    """Journal des appels"""
    flash('Fonctionnalité en cours de développement', 'info')
    return render_template('en_developpement.html')

@app.route('/nouveaux-patients')
def nouveaux_patients():
    """Liste des nouveaux patients"""
    flash('Fonctionnalité en cours de développement', 'info')
    return render_template('en_developpement.html')

@app.route('/salle-attente')
def salle_attente():
    """Gestion de la salle d'attente"""
    flash('Fonctionnalité en cours de développement', 'info')
    return render_template('en_developpement.html')

@app.route('/nouveau-rdv', methods=['GET', 'POST'])
def nouveau_rdv():
    """Nouveau rendez-vous (secrétaire)"""
    if request.method == 'POST':
        flash('Rendez-vous créé avec succès', 'success')
        return redirect('/secretaire/dashboard')
    flash('Fonctionnalité en cours de développement', 'info')
    return render_template('en_developpement.html')

@app.route('/enregistrer-patient', methods=['GET', 'POST'])
def enregistrer_patient():
    """Enregistrer un nouveau patient"""
    if request.method == 'POST':
        flash('Patient enregistré avec succès', 'success')
        return redirect('/secretaire/dashboard')
    flash('Fonctionnalité en cours de développement', 'info')
    return render_template('en_developpement.html')

@app.route('/gestion-planning')
def gestion_planning():
    """Gestion du planning général"""
    flash('Fonctionnalité en cours de développement', 'info')
    return render_template('en_developpement.html')

@app.route('/facturation')
def facturation():
    """Gestion de la facturation"""
    flash('Fonctionnalité en cours de développement', 'info')
    return render_template('en_developpement.html')

@app.route('/medecins')
def medecins():
    """Liste des médecins"""
    try:
        # Pour l'instant, liste vide jusqu'à correction des modèles
        medecins = []
        return render_template('medecins/liste.html', medecins=medecins)
    except Exception as e:
        flash(f'Erreur lors du chargement des médecins: {str(e)}', 'error')
        return render_template('medecins/liste.html', medecins=[])

@app.route('/rendez-vous')
def rendez_vous():
    """Liste des rendez-vous"""
    try:
        # Pour l'instant, liste vide jusqu'à correction des modèles
        rdv = []
        return render_template('rendez_vous/liste.html', rendez_vous=rdv)
    except Exception as e:
        flash(f'Erreur lors du chargement des rendez-vous: {str(e)}', 'error')
        return render_template('rendez_vous/liste.html', rendez_vous=[])

@app.route('/planning')
def planning():
    """Planning du jour"""
    try:
        # Pour l'instant, liste vide jusqu'à correction des modèles
        aujourd_hui = date.today()
        rdv_jour = []
        return render_template('rendez_vous/planning.html', rendez_vous=rdv_jour, date=aujourd_hui)
    except Exception as e:
        flash(f'Erreur lors du chargement du planning: {str(e)}', 'error')
        return render_template('rendez_vous/planning.html', rendez_vous=[], date=aujourd_hui)

@app.route('/nouveau-patient', methods=['GET', 'POST'])
def nouveau_patient():
    """Formulaire nouveau patient"""
    if request.method == 'POST':
        # Récupérer les données du formulaire
        nom = request.form.get('nom')
        prenom = request.form.get('prenom')
        cin = request.form.get('cin')
        telephone = request.form.get('telephone')
        email = request.form.get('email')

        # Pour l'instant, on simule l'ajout (à remplacer par vraie BDD)
        flash(f'Patient {prenom} {nom} ajouté avec succès !', 'success')
        return redirect('/admin/dashboard')

    return render_template('patients/nouveau.html')

@app.route('/nouveau-medecin', methods=['GET', 'POST'])
def nouveau_medecin():
    """Formulaire nouveau médecin"""
    if request.method == 'POST':
        # Récupérer les données du formulaire
        nom = request.form.get('nom')
        prenom = request.form.get('prenom')
        specialite = request.form.get('specialite')
        telephone = request.form.get('telephone')
        email = request.form.get('email')

        # Pour l'instant, on simule l'ajout (à remplacer par vraie BDD)
        flash(f'Médecin {prenom} {nom} ajouté avec succès !', 'success')
        return redirect('/admin/dashboard')

    return render_template('medecins/nouveau.html')

@app.route('/nouveau-rendez-vous', methods=['GET', 'POST'])
def nouveau_rendez_vous():
    """Formulaire nouveau rendez-vous"""
    if request.method == 'POST':
        # Récupérer les données du formulaire
        patient_id = request.form.get('patient_id')
        medecin_id = request.form.get('medecin_id')
        date_rdv = request.form.get('date_rdv')
        heure_rdv = request.form.get('heure_rdv')
        motif = request.form.get('motif')

        # Pour l'instant, on simule l'ajout (à remplacer par vraie BDD)
        flash(f'Rendez-vous programmé avec succès pour le {date_rdv} à {heure_rdv}', 'success')
        return redirect('/admin/dashboard')

    try:
        # Pour l'instant, listes vides jusqu'à correction des modèles
        patients = []
        medecins = []
        return render_template('rendez_vous/nouveau.html', patients=patients, medecins=medecins)
    except Exception as e:
        flash(f'Erreur lors du chargement du formulaire: {str(e)}', 'error')
        return render_template('rendez_vous/nouveau.html', patients=[], medecins=[])

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)