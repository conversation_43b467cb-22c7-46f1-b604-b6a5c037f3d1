﻿from flask_sqlalchemy import SQLAlchemy
from datetime import datetime, timedelta

# Import de l'instance db depuis database
from database import db

class RendezVous(db.Model):
    __tablename__ = 'rendez_vous'
    
    id = db.Column(db.Integer, primary_key=True)
    patient_id = db.Column(db.<PERSON>ger, db.<PERSON><PERSON>('patients.id'), nullable=False)
    medecin_id = db.Column(db.Integer, db.<PERSON>('medecins.id'), nullable=False)
    date_rdv = db.Column(db.DateTime, nullable=False)
    duree = db.Column(db.Integer, default=30)  # durée en minutes
    motif = db.Column(db.Text)
    statut = db.Column(db.Enum('planifie', 'confirme', 'annule', 'termine', 'en_cours'), default='planifie')
    notes = db.Column(db.Text)
    
    # Nouveaux champs pour la gestion améliorée
    type_prise_rdv = db.Column(db.Enum('en_ligne', 'telephone', 'papier', 'urgence'), default='en_ligne')
    numero_telephone_contact = db.Column(db.String(20))
    rappel_envoye = db.Column(db.Boolean, default=False)
    date_rappel = db.Column(db.DateTime)
    priorite = db.Column(db.Enum('normale', 'urgente', 'tres_urgente'), default='normale')
    
    # Informations de facturation
    tarif = db.Column(db.Float)
    facture = db.Column(db.Boolean, default=False)
    
    # Métadonnées
    date_creation = db.Column(db.DateTime, default=datetime.utcnow)
    date_modification = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relations
    consultation = db.relationship('Consultation', backref='rendez_vous', uselist=False, cascade='all, delete-orphan')
    
    def __repr__(self):
        return f'<RendezVous {self.date_rdv} - {self.patient.nom_complet if self.patient else "Patient inconnu"}>'
    
    @property
    def date_formatee(self):
        return self.date_rdv.strftime('%d/%m/%Y à %H:%M')
    
    @property
    def heure_fin(self):
        return self.date_rdv + timedelta(minutes=self.duree)
    
    @property
    def heure_fin_formatee(self):
        return self.heure_fin.strftime('%H:%M')
    
    @property
    def est_aujourd_hui(self):
        from datetime import date
        return self.date_rdv.date() == date.today()
    
    @property
    def est_passe(self):
        return self.date_rdv < datetime.now()
    
    @property
    def temps_restant(self):
        if self.est_passe:
            return "Passé"
        delta = self.date_rdv - datetime.now()
        if delta.days > 0:
            return f"Dans {delta.days} jour(s)"
        elif delta.seconds > 3600:
            heures = delta.seconds // 3600
            return f"Dans {heures}h"
        elif delta.seconds > 60:
            minutes = delta.seconds // 60
            return f"Dans {minutes}min"
        else:
            return "Imminent"
    
    def to_dict(self):
        return {
            'id': self.id,
            'patient_id': self.patient_id,
            'medecin_id': self.medecin_id,
            'patient_nom': self.patient.nom_complet if self.patient else None,
            'medecin_nom': self.medecin.nom_complet if self.medecin else None,
            'date_rdv': self.date_rdv.isoformat() if self.date_rdv else None,
            'date_formatee': self.date_formatee,
            'duree': self.duree,
            'motif': self.motif,
            'statut': self.statut,
            'notes': self.notes,
            'type_prise_rdv': self.type_prise_rdv,
            'priorite': self.priorite,
            'tarif': self.tarif,
            'temps_restant': self.temps_restant
        }
