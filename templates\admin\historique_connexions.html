{% extends "base.html" %}

{% block title %}Historique des Connexions - Ilaji Medical{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="display-6 text-info mb-1">
                    <i class="fas fa-history me-3"></i>Historique des Connexions
                </h1>
                <p class="text-muted mb-0">Activité de connexion de {{ user.prenom }} {{ user.nom }}</p>
            </div>
            <div>
                <a href="/admin/utilisateur/{{ user.id }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Retour aux détails
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Informations utilisateur -->
    <div class="col-lg-4">
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-gradient-info text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user me-2"></i>Utilisateur
                </h5>
            </div>
            <div class="card-body">
                <div class="text-center">
                    <div class="bg-info bg-gradient rounded-circle p-3 mx-auto mb-3" style="width: 80px; height: 80px;">
                        {% if user_type == 'admin' %}
                            <i class="fas fa-user-shield fa-2x text-white"></i>
                        {% else %}
                            <i class="fas fa-user fa-2x text-white"></i>
                        {% endif %}
                    </div>
                    <h6 class="mb-1">{{ user.prenom }} {{ user.nom }}</h6>
                    {% if user_type == 'admin' %}
                        <span class="badge bg-danger mb-2">{{ user.role }}</span>
                    {% else %}
                        <span class="badge bg-info mb-2">Utilisateur</span>
                    {% endif %}
                    <p class="text-muted small mb-0">
                        <i class="fas fa-envelope me-1"></i>{{ user.email }}
                    </p>
                </div>
            </div>
        </div>
        
        <!-- Statistiques de connexion -->
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>Statistiques
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6 mb-3">
                        <div class="border-end">
                            <h4 class="text-success mb-1">{{ historique|selectattr("statut", "equalto", "Succès")|list|length }}</h4>
                            <small class="text-muted">Connexions réussies</small>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <h4 class="text-danger mb-1">{{ historique|selectattr("statut", "equalto", "Échec")|list|length }}</h4>
                        <small class="text-muted">Tentatives échouées</small>
                    </div>
                    <div class="col-12">
                        <hr>
                        <small class="text-muted">
                            <i class="fas fa-clock me-1"></i>
                            Dernière connexion : {{ historique[0].date }} à {{ historique[0].heure }}
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Historique détaillé -->
    <div class="col-lg-8">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i>Historique détaillé
                    </h5>
                    <div>
                        <button class="btn btn-outline-primary btn-sm" onclick="exporterHistorique()">
                            <i class="fas fa-download me-1"></i>Exporter
                        </button>
                        <button class="btn btn-outline-danger btn-sm" onclick="viderHistorique()">
                            <i class="fas fa-trash me-1"></i>Vider
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body">
                {% if historique %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>Date & Heure</th>
                                <th>Action</th>
                                <th>Statut</th>
                                <th>Adresse IP</th>
                                <th>Navigateur</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for connexion in historique %}
                            <tr>
                                <td>
                                    <div>
                                        <strong>{{ connexion.date }}</strong><br>
                                        <small class="text-muted">{{ connexion.heure }}</small>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-secondary">
                                        {% if connexion.action == 'Connexion' %}
                                            <i class="fas fa-sign-in-alt me-1"></i>
                                        {% elif connexion.action == 'Déconnexion' %}
                                            <i class="fas fa-sign-out-alt me-1"></i>
                                        {% else %}
                                            <i class="fas fa-exclamation-triangle me-1"></i>
                                        {% endif %}
                                        {{ connexion.action }}
                                    </span>
                                </td>
                                <td>
                                    {% if connexion.statut == 'Succès' %}
                                        <span class="badge bg-success">
                                            <i class="fas fa-check me-1"></i>{{ connexion.statut }}
                                        </span>
                                    {% else %}
                                        <span class="badge bg-danger">
                                            <i class="fas fa-times me-1"></i>{{ connexion.statut }}
                                        </span>
                                    {% endif %}
                                </td>
                                <td>
                                    <code class="small">{{ connexion.ip }}</code>
                                </td>
                                <td>
                                    <small class="text-muted">
                                        <i class="fas fa-globe me-1"></i>{{ connexion.navigateur }}
                                    </small>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                <nav aria-label="Navigation historique">
                    <ul class="pagination pagination-sm justify-content-center">
                        <li class="page-item disabled">
                            <a class="page-link" href="#" tabindex="-1">Précédent</a>
                        </li>
                        <li class="page-item active">
                            <a class="page-link" href="#">1</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="#">2</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="#">3</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="#">Suivant</a>
                        </li>
                    </ul>
                </nav>
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-history fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">Aucun historique de connexion</h5>
                    <p class="text-muted">L'historique des connexions apparaîtra ici une fois que l'utilisateur se connectera.</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Alertes de sécurité -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-gradient-warning text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-shield-alt me-2"></i>Alertes de sécurité
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="alert alert-success" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            <strong>Aucune activité suspecte</strong><br>
                            <small>Toutes les connexions semblent normales</small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="alert alert-info" role="alert">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Adresse IP habituelle</strong><br>
                            <small>Connexions depuis l'IP habituelle</small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="alert alert-warning" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>1 tentative échouée</strong><br>
                            <small>Vérifiez les tentatives de connexion</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function exporterHistorique() {
    alert('Fonctionnalité d\'export en cours de développement.\n\nL\'historique sera exporté au format CSV avec :\n- Date et heure\n- Action\n- Statut\n- Adresse IP\n- Navigateur');
}

function viderHistorique() {
    if (confirm('Êtes-vous sûr de vouloir vider l\'historique des connexions ?\n\nCette action est irréversible et supprimera toutes les données d\'historique.')) {
        alert('Historique vidé avec succès !');
        // Ici on pourrait rediriger vers une route qui vide l'historique
    }
}
</script>
{% endblock %}
