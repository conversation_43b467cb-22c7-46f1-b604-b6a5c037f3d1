﻿{% extends "base.html" %}

{% block title %}Tableau de bord - Ilaji Medical{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="display-6 text-primary mb-1">
                    <i class="fas fa-tachometer-alt me-3"></i>Tableau de bord
                </h1>
                <p class="text-muted mb-0">Vue d'ensemble de votre cabinet médical</p>
                {% if current_user.is_authenticated %}
                <small class="text-success">
                    <i class="fas fa-user me-1"></i>Connecté en tant que {{ current_user.role.value.title() }}
                </small>
                {% endif %}
            </div>
            <div class="text-end">
                <div class="badge bg-primary fs-6 px-3 py-2">
                    <i class="fas fa-calendar me-2"></i>{{ date_actuelle }}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Statistiques principales -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-0 shadow-sm h-100 card-hover">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-primary bg-gradient rounded-circle p-3">
                            <i class="fas fa-users fa-2x text-white"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h3 class="mb-1 fw-bold text-primary">{{ total_patients }}</h3>
                        <p class="text-muted mb-0">Patients actifs</p>
                    </div>
                </div>
            </div>
            <div class="card-footer bg-transparent border-0">
                <a href="{{ url_for('patients') }}" class="btn btn-outline-primary btn-sm">
                    <i class="fas fa-eye me-1"></i>Voir tous
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-0 shadow-sm h-100 card-hover">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-success bg-gradient rounded-circle p-3">
                            <i class="fas fa-user-md fa-2x text-white"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h3 class="mb-1 fw-bold text-success">{{ total_medecins }}</h3>
                        <p class="text-muted mb-0">Médecins</p>
                    </div>
                </div>
            </div>
            <div class="card-footer bg-transparent border-0">
                <a href="{{ url_for('medecins') }}" class="btn btn-outline-success btn-sm">
                    <i class="fas fa-eye me-1"></i>Voir tous
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-0 shadow-sm h-100 card-hover">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-info bg-gradient rounded-circle p-3">
                            <i class="fas fa-calendar-day fa-2x text-white"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h3 class="mb-1 fw-bold text-info">{{ total_rdv_aujourd_hui }}</h3>
                        <p class="text-muted mb-0">RDV aujourd'hui</p>
                    </div>
                </div>
            </div>
            <div class="card-footer bg-transparent border-0">
                <a href="{{ url_for('planning') }}" class="btn btn-outline-info btn-sm">
                    <i class="fas fa-calendar me-1"></i>Planning
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-0 shadow-sm h-100 card-hover">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-warning bg-gradient rounded-circle p-3">
                            <i class="fas fa-chart-line fa-2x text-white"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h3 class="mb-1 fw-bold text-warning">{{ rdv_semaine }}</h3>
                        <p class="text-muted mb-0">RDV cette semaine</p>
                    </div>
                </div>
            </div>
            <div class="card-footer bg-transparent border-0">
                <a href="{{ url_for('rendez_vous') }}" class="btn btn-outline-warning btn-sm">
                    <i class="fas fa-eye me-1"></i>Voir tous
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Actions rapides -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-gradient-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i>Actions rapides
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('nouveau_patient') }}" class="btn btn-outline-primary w-100 py-3">
                            <i class="fas fa-user-plus fa-2x mb-2 d-block"></i>
                            Nouveau Patient
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('nouveau_rendez_vous') }}" class="btn btn-outline-success w-100 py-3">
                            <i class="fas fa-calendar-plus fa-2x mb-2 d-block"></i>
                            Nouveau RDV
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('planning') }}" class="btn btn-outline-info w-100 py-3">
                            <i class="fas fa-calendar-day fa-2x mb-2 d-block"></i>
                            Planning du jour
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <button class="btn btn-outline-warning w-100 py-3" onclick="window.print()">
                            <i class="fas fa-print fa-2x mb-2 d-block"></i>
                            Imprimer planning
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Rendez-vous urgents et prochains -->
<div class="row">
    <!-- Rendez-vous urgents -->
    {% if rdv_urgents %}
    <div class="col-lg-6 mb-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-header bg-danger text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>Rendez-vous urgents
                </h5>
            </div>
            <div class="card-body">
                {% for rdv in rdv_urgents %}
                <div class="d-flex align-items-center mb-3 p-3 bg-light rounded">
                    <div class="flex-shrink-0">
                        <div class="badge bg-{{ 'danger' if rdv.priorite == 'tres_urgente' else 'warning' }} rounded-pill">
                            {{ 'TRÈS URGENT' if rdv.priorite == 'tres_urgente' else 'URGENT' }}
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h6 class="mb-1">{{ rdv.patient.nom_complet }}</h6>
                        <p class="text-muted mb-1">
                            <i class="fas fa-clock me-1"></i>{{ rdv.date_formatee }}
                        </p>
                        <p class="text-muted mb-0">
                            <i class="fas fa-user-md me-1"></i>{{ rdv.medecin.nom_complet }}
                        </p>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
    {% endif %}
    
    <!-- Prochains rendez-vous -->
    <div class="col-lg-{{ '6' if rdv_urgents else '12' }} mb-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-clock me-2"></i>Prochains rendez-vous
                </h5>
            </div>
            <div class="card-body">
                {% if prochains_rdv %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Patient</th>
                                    <th>Date & Heure</th>
                                    <th>Médecin</th>
                                    <th>Statut</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for rdv in prochains_rdv %}
                                <tr>
                                    <td>
                                        <strong>{{ rdv.patient.nom_complet }}</strong>
                                        {% if rdv.priorite != 'normale' %}
                                            <span class="badge bg-{{ 'danger' if rdv.priorite == 'tres_urgente' else 'warning' }} ms-1">
                                                {{ 'URGENT' if rdv.priorite == 'urgente' else 'TRÈS URGENT' }}
                                            </span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div>{{ rdv.date_formatee }}</div>
                                        <small class="text-muted">{{ rdv.temps_restant }}</small>
                                    </td>
                                    <td>{{ rdv.medecin.nom_complet }}</td>
                                    <td>
                                        <span class="badge bg-{{ 'success' if rdv.statut == 'confirme' else 'primary' }}">
                                            {{ rdv.statut.replace('_', ' ').title() }}
                                        </span>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Aucun rendez-vous programmé</h5>
                        <p class="text-muted">Commencez par créer votre premier rendez-vous</p>
                        <a href="{{ url_for('nouveau_rendez_vous') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i>Créer un rendez-vous
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Mise à jour de l'heure en temps réel
function updateTime() {
    const now = new Date();
    const timeString = now.toLocaleTimeString('fr-FR');
    document.getElementById('current-time').textContent = timeString;
}

// Mettre à jour l'heure toutes les secondes
setInterval(updateTime, 1000);
updateTime();

// Animation des cartes au chargement
document.addEventListener('DOMContentLoaded', function() {
    const cards = document.querySelectorAll('.card-hover');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        setTimeout(() => {
            card.style.transition = 'all 0.5s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
});
</script>
{% endblock %}
