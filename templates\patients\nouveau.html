﻿{% extends "base.html" %}

{% block title %}Nouveau Patient - Ilaji Medical{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="display-6 text-primary mb-1">
                    <i class="fas fa-user-plus me-3"></i>Nouveau Patient
                </h1>
                <p class="text-muted mb-0">Ajoutez un nouveau patient au système</p>
            </div>
            <div>
                <a href="{{ url_for('patients') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Retour à la liste
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-xl-10">
        <form method="POST" class="needs-validation" novalidate>
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-gradient-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-user me-2"></i>Informations du Patient
                    </h5>
                </div>
                <div class="card-body">
                    <!-- Informations personnelles -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-primary border-bottom pb-2 mb-3">
                                <i class="fas fa-id-card me-2"></i>Informations Personnelles
                            </h6>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="nom" class="form-label">Nom *</label>
                                <input type="text" class="form-control" id="nom" name="nom" required>
                                <div class="invalid-feedback">
                                    Le nom est obligatoire
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="prenom" class="form-label">Prénom *</label>
                                <input type="text" class="form-control" id="prenom" name="prenom" required>
                                <div class="invalid-feedback">
                                    Le prénom est obligatoire
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="date_naissance" class="form-label">Date de naissance *</label>
                                <input type="date" class="form-control" id="date_naissance" name="date_naissance" required>
                                <div class="invalid-feedback">
                                    La date de naissance est obligatoire
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="sexe" class="form-label">Sexe *</label>
                                <select class="form-select" id="sexe" name="sexe" required>
                                    <option value="">Sélectionner...</option>
                                    <option value="M">Homme</option>
                                    <option value="F">Femme</option>
                                </select>
                                <div class="invalid-feedback">
                                    Le sexe est obligatoire
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="situation_familiale" class="form-label">Situation familiale</label>
                                <select class="form-select" id="situation_familiale" name="situation_familiale">
                                    <option value="">Non renseigné</option>
                                    <option value="celibataire">Célibataire</option>
                                    <option value="marie">Marié(e)</option>
                                    <option value="divorce">Divorcé(e)</option>
                                    <option value="veuf">Veuf/Veuve</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Informations de contact -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-primary border-bottom pb-2 mb-3">
                                <i class="fas fa-phone me-2"></i>Informations de Contact
                            </h6>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="telephone" class="form-label">Téléphone</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-phone"></i>
                                    </span>
                                    <input type="tel" class="form-control" id="telephone" name="telephone" 
                                           placeholder="01.23.45.67.89">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="email" class="form-label">Email</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-envelope"></i>
                                    </span>
                                    <input type="email" class="form-control" id="email" name="email" 
                                           placeholder="<EMAIL>">
                                </div>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="mb-3">
                                <label for="adresse" class="form-label">Adresse complète</label>
                                <textarea class="form-control" id="adresse" name="adresse" rows="3" 
                                          placeholder="Numéro, rue, code postal, ville"></textarea>
                            </div>
                        </div>
                    </div>

                    <!-- Informations professionnelles -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-primary border-bottom pb-2 mb-3">
                                <i class="fas fa-briefcase me-2"></i>Informations Professionnelles
                            </h6>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="profession" class="form-label">Profession</label>
                                <input type="text" class="form-control" id="profession" name="profession" 
                                       placeholder="Ex: Enseignant, Ingénieur...">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="numero_securite_sociale" class="form-label">Numéro de sécurité sociale</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-id-card"></i>
                                    </span>
                                    <input type="text" class="form-control" id="numero_securite_sociale" 
                                           name="numero_securite_sociale" placeholder="1234567890123" maxlength="15">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Contact d'urgence -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-primary border-bottom pb-2 mb-3">
                                <i class="fas fa-exclamation-triangle me-2"></i>Contact d'Urgence
                            </h6>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="personne_a_contacter" class="form-label">Personne à contacter</label>
                                <input type="text" class="form-control" id="personne_a_contacter" 
                                       name="personne_a_contacter" placeholder="Nom et lien de parenté">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="telephone_urgence" class="form-label">Téléphone d'urgence</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-phone-alt"></i>
                                    </span>
                                    <input type="tel" class="form-control" id="telephone_urgence" 
                                           name="telephone_urgence" placeholder="01.23.45.67.89">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Informations médicales -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h6 class="text-primary border-bottom pb-2 mb-3">
                                <i class="fas fa-stethoscope me-2"></i>Informations Médicales
                            </h6>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="medecin_traitant" class="form-label">Médecin traitant</label>
                                <input type="text" class="form-control" id="medecin_traitant" 
                                       name="medecin_traitant" placeholder="Dr. Nom Prénom">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="mutuelle" class="form-label">Mutuelle</label>
                                <input type="text" class="form-control" id="mutuelle" name="mutuelle" 
                                       placeholder="Nom de la mutuelle">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="numero_mutuelle" class="form-label">Numéro de mutuelle</label>
                                <input type="text" class="form-control" id="numero_mutuelle" 
                                       name="numero_mutuelle" placeholder="Numéro d'adhérent">
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="card-footer bg-light">
                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('patients') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>Annuler
                        </a>
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-save me-2"></i>Enregistrer le Patient
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Validation du formulaire
    const form = document.querySelector('.needs-validation');
    
    form.addEventListener('submit', function(event) {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        }
        form.classList.add('was-validated');
    });
    
    // Formatage automatique des numéros de téléphone
    const phoneInputs = document.querySelectorAll('input[type="tel"]');
    phoneInputs.forEach(input => {
        input.addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            if (value.length >= 10) {
                value = value.substring(0, 10);
                value = value.replace(/(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})/, '....');
            }
            e.target.value = value;
        });
    });
    
    // Formatage du numéro de sécurité sociale
    const ssnInput = document.getElementById('numero_securite_sociale');
    if (ssnInput) {
        ssnInput.addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            if (value.length > 15) {
                value = value.substring(0, 15);
            }
            e.target.value = value;
        });
    }
    
    // Calcul automatique de l'âge
    const dateNaissanceInput = document.getElementById('date_naissance');
    if (dateNaissanceInput) {
        dateNaissanceInput.addEventListener('change', function(e) {
            const birthDate = new Date(e.target.value);
            const age = IlajiMedical.calculateAge(birthDate);
            
            if (age >= 0) {
                // Afficher l'âge à côté du champ
                let ageDisplay = document.getElementById('age-display');
                if (!ageDisplay) {
                    ageDisplay = document.createElement('small');
                    ageDisplay.id = 'age-display';
                    ageDisplay.className = 'text-muted ms-2';
                    e.target.parentNode.appendChild(ageDisplay);
                }
                ageDisplay.textContent = ( ans);
            }
        });
    }
    
    // Animation des sections
    const sections = document.querySelectorAll('.row.mb-4');
    sections.forEach((section, index) => {
        section.style.opacity = '0';
        section.style.transform = 'translateY(20px)';
        setTimeout(() => {
            section.style.transition = 'all 0.5s ease';
            section.style.opacity = '1';
            section.style.transform = 'translateY(0)';
        }, index * 100);
    });
});
</script>
{% endblock %}
