from functools import wraps
from flask import abort, redirect, url_for, flash, request
from flask_login import current_user
from models.user import User<PERSON><PERSON>

def login_required_with_role(*roles):
    """
    Décorateur qui vérifie que l'utilisateur est connecté et a l'un des rôles spécifiés
    
    Usage:
    @login_required_with_role(UserRole.ADMIN, UserRole.MEDECIN)
    def ma_route():
        pass
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not current_user.is_authenticated:
                flash('Vous devez être connecté pour accéder à cette page.', 'warning')
                return redirect(url_for('auth.login', next=request.url))
            
            if not current_user.is_active:
                flash('Votre compte a été désactivé. Contactez l\'administrateur.', 'error')
                return redirect(url_for('auth.login'))
            
            if roles and current_user.role not in roles:
                flash('Vous n\'avez pas les permissions nécessaires pour accéder à cette page.', 'error')
                abort(403)
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def admin_required(f):
    """Décorateur pour les routes réservées aux administrateurs"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated:
            flash('Vous devez être connecté pour accéder à cette page.', 'warning')
            return redirect(url_for('auth.login', next=request.url))
        
        if not current_user.has_role(UserRole.ADMIN):
            flash('Accès réservé aux administrateurs.', 'error')
            abort(403)
        
        return f(*args, **kwargs)
    return decorated_function

def medecin_required(f):
    """Décorateur pour les routes réservées aux médecins"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated:
            flash('Vous devez être connecté pour accéder à cette page.', 'warning')
            return redirect(url_for('auth.login', next=request.url))
        
        if not current_user.has_role(UserRole.MEDECIN):
            flash('Accès réservé aux médecins.', 'error')
            abort(403)
        
        return f(*args, **kwargs)
    return decorated_function

def secretaire_required(f):
    """Décorateur pour les routes réservées aux secrétaires"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated:
            flash('Vous devez être connecté pour accéder à cette page.', 'warning')
            return redirect(url_for('auth.login', next=request.url))
        
        if not current_user.has_role(UserRole.SECRETAIRE):
            flash('Accès réservé aux secrétaires.', 'error')
            abort(403)
        
        return f(*args, **kwargs)
    return decorated_function

def patient_required(f):
    """Décorateur pour les routes réservées aux patients"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated:
            flash('Vous devez être connecté pour accéder à cette page.', 'warning')
            return redirect(url_for('auth.login', next=request.url))
        
        if not current_user.has_role(UserRole.PATIENT):
            flash('Accès réservé aux patients.', 'error')
            abort(403)
        
        return f(*args, **kwargs)
    return decorated_function

def permission_required(permission):
    """
    Décorateur qui vérifie qu'un utilisateur a une permission spécifique
    
    Usage:
    @permission_required('manage_patients')
    def ma_route():
        pass
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not current_user.is_authenticated:
                flash('Vous devez être connecté pour accéder à cette page.', 'warning')
                return redirect(url_for('auth.login', next=request.url))
            
            if not current_user.has_permission(permission):
                flash(f'Vous n\'avez pas la permission "{permission}" pour accéder à cette page.', 'error')
                abort(403)
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def staff_required(f):
    """Décorateur pour les routes réservées au personnel (admin, médecin, secrétaire)"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated:
            flash('Vous devez être connecté pour accéder à cette page.', 'warning')
            return redirect(url_for('auth.login', next=request.url))
        
        staff_roles = [UserRole.ADMIN, UserRole.MEDECIN, UserRole.SECRETAIRE]
        if current_user.role not in staff_roles:
            flash('Accès réservé au personnel médical.', 'error')
            abort(403)
        
        return f(*args, **kwargs)
    return decorated_function

def verified_required(f):
    """Décorateur qui vérifie que l'utilisateur a vérifié son compte"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated:
            flash('Vous devez être connecté pour accéder à cette page.', 'warning')
            return redirect(url_for('auth.login', next=request.url))
        
        if not current_user.is_verified:
            flash('Vous devez vérifier votre compte pour accéder à cette page.', 'warning')
            return redirect(url_for('auth.verify_email'))
        
        return f(*args, **kwargs)
    return decorated_function
