{% extends "base.html" %}

{% block title %}Modifier Administrateur - Il<PERSON><PERSON> Medical{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="display-6 text-danger mb-1">
                    <i class="fas fa-user-shield me-3"></i>Modifier Administrateur
                </h1>
                <p class="text-muted mb-0">Modification des informations de {{ admin.prenom }} {{ admin.nom }}</p>
            </div>
            <div>
                <a href="/admin/utilisateurs" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Retour à la liste
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card border-0 shadow-lg">
            <div class="card-header bg-gradient-danger text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user-shield me-2"></i>Informations administrateur
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" class="needs-validation" novalidate>
                    <div class="row">
                        <!-- Informations personnelles -->
                        <div class="col-md-6">
                            <h6 class="text-primary mb-3">
                                <i class="fas fa-user me-2"></i>Informations personnelles
                            </h6>
                            
                            <div class="mb-3">
                                <label for="prenom" class="form-label">Prénom *</label>
                                <input type="text" class="form-control" id="prenom" name="prenom" 
                                       value="{{ admin.prenom }}" required>
                                <div class="invalid-feedback">
                                    Veuillez saisir le prénom.
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="nom" class="form-label">Nom *</label>
                                <input type="text" class="form-control" id="nom" name="nom" 
                                       value="{{ admin.nom }}" required>
                                <div class="invalid-feedback">
                                    Veuillez saisir le nom.
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="telephone" class="form-label">Téléphone</label>
                                <input type="tel" class="form-control" id="telephone" name="telephone" 
                                       value="{{ admin.telephone }}" placeholder="06.12.34.56.78">
                            </div>
                            
                            <div class="mb-3">
                                <label for="email" class="form-label">Email</label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="{{ admin.email }}" placeholder="<EMAIL>">
                            </div>
                        </div>
                        
                        <!-- Informations administratives -->
                        <div class="col-md-6">
                            <h6 class="text-danger mb-3">
                                <i class="fas fa-shield-alt me-2"></i>Informations administratives
                            </h6>
                            
                            <div class="mb-3">
                                <label for="username" class="form-label">Nom d'utilisateur</label>
                                <input type="text" class="form-control" id="username" name="username" 
                                       value="{{ admin.username }}" readonly>
                                <div class="form-text">Le nom d'utilisateur ne peut pas être modifié</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="role" class="form-label">Rôle administrateur *</label>
                                <select class="form-select" id="role" name="role" required>
                                    <option value="">Choisir un rôle</option>
                                    <option value="Administrateur Principal" {{ 'selected' if admin.role == 'Administrateur Principal' }}>Administrateur Principal</option>
                                    <option value="Administrateur Système" {{ 'selected' if admin.role == 'Administrateur Système' }}>Administrateur Système</option>
                                    <option value="Administrateur Médical" {{ 'selected' if admin.role == 'Administrateur Médical' }}>Administrateur Médical</option>
                                    <option value="Super Administrateur" {{ 'selected' if admin.role == 'Super Administrateur' }}>Super Administrateur</option>
                                </select>
                                <div class="invalid-feedback">
                                    Veuillez choisir un rôle.
                                </div>
                            </div>
                            
                            <div class="alert alert-warning" role="alert">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <strong>Attention :</strong> La modification des droits administrateur peut affecter l'accès au système.
                            </div>
                            
                            <div class="alert alert-info" role="alert">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>Note :</strong> Pour modifier le mot de passe, contactez le support technique.
                            </div>
                        </div>
                    </div>
                    
                    <!-- Boutons d'action -->
                    <div class="row">
                        <div class="col-12">
                            <div class="d-flex justify-content-between">
                                <a href="/admin/utilisateurs" class="btn btn-secondary">
                                    <i class="fas fa-times me-2"></i>Annuler
                                </a>
                                <button type="submit" class="btn btn-danger">
                                    <i class="fas fa-save me-2"></i>Enregistrer les modifications
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
// Validation Bootstrap
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>
{% endblock %}
