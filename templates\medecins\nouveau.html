{% extends "base.html" %}

{% block title %}Nouveau médecin - Ilaji Medical{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="display-6 text-primary mb-1">
                    <i class="fas fa-user-md me-3"></i>Nouveau médecin
                </h1>
                <p class="text-muted mb-0">Ajouter un nouveau médecin au cabinet</p>
            </div>
            <div>
                <a href="{{ url_for('medecins') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Retour à la liste
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8 mx-auto">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-gradient-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-plus me-2"></i>Informations du médecin
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="nom" class="form-label">Nom *</label>
                            <input type="text" class="form-control" id="nom" name="nom" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="prenom" class="form-label">Prénom *</label>
                            <input type="text" class="form-control" id="prenom" name="prenom" required>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="specialite" class="form-label">Spécialité *</label>
                            <select class="form-select" id="specialite" name="specialite" required>
                                <option value="">Choisir une spécialité</option>
                                <option value="Médecine générale">Médecine générale</option>
                                <option value="Cardiologie">Cardiologie</option>
                                <option value="Dermatologie">Dermatologie</option>
                                <option value="Gynécologie">Gynécologie</option>
                                <option value="Pédiatrie">Pédiatrie</option>
                                <option value="Orthopédie">Orthopédie</option>
                                <option value="Ophtalmologie">Ophtalmologie</option>
                                <option value="ORL">ORL</option>
                                <option value="Psychiatrie">Psychiatrie</option>
                                <option value="Radiologie">Radiologie</option>
                                <option value="Autre">Autre</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="tarif_consultation" class="form-label">Tarif consultation (€)</label>
                            <input type="number" class="form-control" id="tarif_consultation" name="tarif_consultation" step="0.01" min="0">
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="telephone" class="form-label">Téléphone</label>
                            <input type="tel" class="form-control" id="telephone" name="telephone">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">Email</label>
                            <input type="email" class="form-control" id="email" name="email">
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="adresse" class="form-label">Adresse</label>
                        <textarea class="form-control" id="adresse" name="adresse" rows="3"></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="numero_rpps" class="form-label">Numéro RPPS</label>
                            <input type="text" class="form-control" id="numero_rpps" name="numero_rpps">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="numero_adeli" class="form-label">Numéro ADELI</label>
                            <input type="text" class="form-control" id="numero_adeli" name="numero_adeli">
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="secteur_conventionnement" class="form-label">Secteur de conventionnement</label>
                        <select class="form-select" id="secteur_conventionnement" name="secteur_conventionnement">
                            <option value="">Choisir un secteur</option>
                            <option value="secteur_1">Secteur 1</option>
                            <option value="secteur_2">Secteur 2</option>
                            <option value="non_conventionne">Non conventionné</option>
                        </select>
                    </div>
                    
                    <div class="d-flex justify-content-end gap-2">
                        <a href="{{ url_for('medecins') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>Annuler
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Enregistrer
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
